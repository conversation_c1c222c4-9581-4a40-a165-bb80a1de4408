/* 球队页面特定样式 */

/* 页面加载指示器 */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 页面头部增强 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.page-header h1 {
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-size: 1rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 搜索和筛选区域 */
.search-filter-section {
    margin-bottom: 1.5rem;
}

.filter-section {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-top: 1rem;
}

.filter-toggle {
    padding: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
    border-radius: var(--border-radius);
    transition: background-color 0.3s ease;
}

.filter-toggle:hover {
    background: var(--bg-secondary);
}

.filter-content {
    padding: 0 1rem 1rem 1rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: white;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(46, 204, 113, 0.2);
}

/* 筛选结果统计 */
.filter-stats {
    padding: 0.75rem 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.filter-stats .stats-text {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-stats strong {
    color: var(--primary-color);
    font-weight: 600;
}

/* 球队卡片网格 */
.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 球队卡片动画 */
.team-card {
    animation: slideInUp 0.6s ease-out both;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 球队卡片 */
.team-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease;
}

.team-card:hover {
    transform: translateY(-5px);
}

.team-card .card-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.team-card .card-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.category-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
}

.team-card .card-body {
    padding: 1rem;
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
}

.team-logo {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.team-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.team-info p {
    margin: 0.5rem 0;
    color: var(--text-secondary);
}

.team-info strong {
    color: var(--text-primary);
}

.team-card .card-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

.view-btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;
}

.view-btn:hover {
    background: var(--primary-dark);
}

/* 球队详情 */
.team-detail {
    padding: 0;
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    max-width: 1200px;
    margin: 0 auto;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.team-detail-header {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 2rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    align-items: start;
}

.team-detail-logo {
    width: 150px;
    height: 150px;
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.team-detail-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.team-detail-info {
    min-width: 0;
    /* 防止flex项目溢出 */
}

.team-detail-info h2 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 600;
}

.team-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.team-meta p {
    margin: 0;
    color: var(--text-secondary);
    padding: 0.5rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
}

.team-meta strong {
    color: var(--text-primary);
    display: block;
    margin-bottom: 0.25rem;
}

.team-description {
    margin-top: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.team-stat-card {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.team-stat-card .stat-label {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.team-stat-card .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* 球员列表 */
.team-players,
.team-matches {
    margin-top: 2rem;
}

.team-players h3,
.team-matches h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.players-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.player-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.player-card h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.player-info p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* 比赛列表 */
.matches-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.match-item {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.match-item:hover {
    background: var(--bg-hover);
}

.match-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.match-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.team-name {
    flex: 1;
    font-weight: 500;
    color: var(--text-primary);
}

.team-name.home {
    text-align: right;
    padding-right: 1rem;
}

.team-name.away {
    text-align: left;
    padding-left: 1rem;
}

.match-score {
    padding: 0.25rem 0.5rem;
    background: rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-sm);
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

.result {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: bold;
}

.result.win {
    background: #4caf50;
    color: white;
}

.result.draw {
    background: #ff9800;
    color: white;
}

.result.loss {
    background: #f44336;
    color: white;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.back-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.back-btn:hover {
    background: var(--secondary-dark);
}

.no-data {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* 通知系统样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    border-left: 4px solid #4caf50;
    color: #2e7d32;
}

.notification-error {
    border-left: 4px solid #f44336;
    color: #c62828;
}

.notification-warning {
    border-left: 4px solid #ff9800;
    color: #ef6c00;
}

.notification-info {
    border-left: 4px solid #2196f3;
    color: #1565c0;
}

.notification i {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification span {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    opacity: 1;
}

/* 增强的无数据状态 */
.no-data {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin: 2rem 0;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--gray-color);
}

.no-data p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.no-data .btn {
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.no-data .btn:hover {
    background: var(--secondary-color);
}

/* 球队统计卡片增强 */
.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.team-stat-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1.5rem 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: transform 0.3s ease;
}

.team-stat-card:hover {
    transform: translateY(-3px);
}

.team-stat-card .stat-label {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.team-stat-card .stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

/* 球员和比赛区域增强 */
.team-players,
.team-matches {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.team-players h3,
.team-matches h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.team-players h3::before {
    content: "👥";
    font-size: 1.2rem;
}

.team-matches h3::before {
    content: "⚽";
    font-size: 1.2rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .teams-grid {
        grid-template-columns: 1fr;
        padding: 0.5rem;
    }

    .team-detail {
        padding: 0;
        margin: 0 0.5rem;
    }

    .team-detail-header {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1rem;
        padding: 1rem;
    }

    .team-detail-logo {
        width: 120px;
        height: 120px;
        margin: 0 auto;
    }

    .team-detail-info h2 {
        font-size: 1.5rem;
    }

    .team-meta {
        grid-template-columns: 1fr;
    }

    .players-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .team-stats {
        grid-template-columns: repeat(2, 1fr);
        padding: 1rem;
    }

    .team-stat-card {
        padding: 1rem 0.5rem;
    }

    .team-stat-card .stat-value {
        font-size: 1.5rem;
    }

    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: none;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-actions {
        justify-content: center;
    }

    .filter-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .team-stats {
        grid-template-columns: 1fr;
    }

    .action-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .page-header h1 {
        font-size: 1.2rem;
    }
}