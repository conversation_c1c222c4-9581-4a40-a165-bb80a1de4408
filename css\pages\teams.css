/*
 * 球队页面特定样式 - HTML5技术展示
 * 使用现代CSS技术：Flexbox、Grid、CSS变量、动画等
 */

/* HTML5 页面加载指示器 */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.9), rgba(39, 174, 96, 0.9));
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(10px);
}

/* 视觉隐藏类 - 用于屏幕阅读器 */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* HTML5 语义化页面头部样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

/* 添加装饰性背景图案 */
.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* HTML5 hgroup 元素样式 */
.page-header hgroup {
    flex: 1;
    z-index: 1;
}

.page-header h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    margin: 0;
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 300;
    line-height: 1.4;
}

/* HTML5 语义化导航按钮组 */
.header-actions {
    display: flex;
    gap: 0.75rem;
    z-index: 1;
    flex-wrap: wrap;
}

.action-btn {
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-width: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn i {
    font-size: 1.2rem;
}

.btn-text {
    font-size: 0.75rem;
    font-weight: 500;
}

/* HTML5 语义化搜索和筛选区域 */
.search-filter-section {
    margin-bottom: 2rem;
}

/* HTML5 details/summary 筛选器样式 */
.filter-section {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-top: 1rem;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.filter-toggle {
    padding: 1.25rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--bg-secondary), white);
    transition: all 0.3s ease;
    border: none;
    width: 100%;
}

.filter-toggle:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.filter-toggle small {
    font-size: 0.75rem;
    opacity: 0.8;
    font-weight: 400;
}

/* HTML5 form 和 fieldset 样式 - 紧凑版本 */
.filter-content {
    padding: 1rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    background: white;
}

.filter-group {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    background: var(--bg-secondary);
    transition: border-color 0.3s ease;
}

.filter-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.1);
}

.filter-group legend {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    background: white;
    border-radius: var(--border-radius-sm);
    margin-bottom: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
    display: block;
}

.filter-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 38px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.2);
    transform: translateY(-1px);
}

.filter-select:hover {
    border-color: var(--secondary-color);
}

/* 筛选操作按钮 - 紧凑版本 */
.filter-actions {
    grid-column: 1 / -1;
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color);
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    height: 38px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
}

.btn-secondary {
    background: var(--gray-color);
    color: white;
}

.btn-secondary:hover {
    background: var(--text-secondary);
    transform: translateY(-2px);
}

/* 筛选结果统计 */
.filter-stats {
    padding: 0.75rem 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.filter-stats .stats-text {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-stats strong {
    color: var(--primary-color);
    font-weight: 600;
}

/* 球队卡片网格 */
.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 球队卡片动画 */
.team-card {
    animation: slideInUp 0.6s ease-out both;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 球队卡片 */
.team-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease;
}

.team-card:hover {
    transform: translateY(-5px);
}

.team-card .card-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.team-card .card-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.category-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
}

.team-card .card-body {
    padding: 1rem;
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
}

.team-logo {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.team-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.team-info p {
    margin: 0.5rem 0;
    color: var(--text-secondary);
}

.team-info strong {
    color: var(--text-primary);
}

.team-card .card-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

.view-btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;
}

.view-btn:hover {
    background: var(--primary-dark);
}

/* HTML5 语义化球队详情区域 - 紧凑版本 */
.team-detail {
    padding: 0.75rem;
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}

/* 详情页面返回按钮区域 - 紧凑版本 */
.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

/* 球队详情头部 - 紧凑版本 */
.team-detail-header {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, white, var(--bg-secondary));
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

/* 球队基本信息区域 - 紧凑版本 */
.team-basic-info {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    flex-wrap: wrap;
}

.team-detail-logo {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: var(--border-radius);
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    border: 1px solid var(--border-color);
}

.team-detail-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.team-detail-info {
    flex: 1;
    min-width: 200px;
}

.team-detail-info h2 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1.2;
}

/* 球队元数据网格 - 紧凑版本 */
.team-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.team-meta-item {
    background: white;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.team-meta-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.team-meta-item .meta-label {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.team-meta-item .meta-value {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
}

/* 球队描述 */
.team-description {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--secondary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    line-height: 1.7;
    color: var(--text-secondary);
    font-size: 1rem;
}

.team-description h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.team-stat-card {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.team-stat-card .stat-label {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.team-stat-card .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* 球员列表 */
.team-players,
.team-matches {
    margin-top: 2rem;
}

.team-players h3,
.team-matches h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.players-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.player-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.player-card h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.player-info p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* 比赛列表 */
.matches-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.match-item {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.match-item:hover {
    background: var(--bg-hover);
}

.match-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.match-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.team-name {
    flex: 1;
    font-weight: 500;
    color: var(--text-primary);
}

.team-name.home {
    text-align: right;
    padding-right: 1rem;
}

.team-name.away {
    text-align: left;
    padding-left: 1rem;
}

.match-score {
    padding: 0.25rem 0.5rem;
    background: rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-sm);
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

.result {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: bold;
}

.result.win {
    background: #4caf50;
    color: white;
}

.result.draw {
    background: #ff9800;
    color: white;
}

.result.loss {
    background: #f44336;
    color: white;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.back-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.back-btn:hover {
    background: var(--secondary-dark);
}

.no-data {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* 通知系统样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    border-left: 4px solid #4caf50;
    color: #2e7d32;
}

.notification-error {
    border-left: 4px solid #f44336;
    color: #c62828;
}

.notification-warning {
    border-left: 4px solid #ff9800;
    color: #ef6c00;
}

.notification-info {
    border-left: 4px solid #2196f3;
    color: #1565c0;
}

.notification i {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification span {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    opacity: 1;
}

/* 增强的无数据状态 */
.no-data {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin: 2rem 0;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--gray-color);
}

.no-data p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.no-data .btn {
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.no-data .btn:hover {
    background: var(--secondary-color);
}

/* 球队统计卡片增强 */
.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.team-stat-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1.5rem 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: transform 0.3s ease;
}

.team-stat-card:hover {
    transform: translateY(-3px);
}

.team-stat-card .stat-label {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.team-stat-card .stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

/* 球员和比赛区域增强 */
.team-players,
.team-matches {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.team-players h3,
.team-matches h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.team-players h3::before {
    content: "👥";
    font-size: 1.2rem;
}

.team-matches h3::before {
    content: "⚽";
    font-size: 1.2rem;
}

/* HTML5 多媒体区域样式 */
.multimedia-section {
    margin-top: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--card-bg), white);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

.multimedia-section h2 {
    margin: 0 0 2rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    text-align: center;
    position: relative;
}

.multimedia-section h2::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

/* HTML5 视频元素样式 */
.video-section {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
}

.video-section h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.video-section h3::before {
    content: "🎥";
    font-size: 1.1rem;
}

#team-video {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* HTML5 音频元素样式 */
.audio-section {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
}

.audio-section h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.audio-section h3::before {
    content: "🎵";
    font-size: 1.1rem;
}

#team-audio {
    width: 100%;
    border-radius: var(--border-radius);
}

/* HTML5 Canvas 图表区域样式 */
.charts-section {
    margin-top: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, white, var(--bg-secondary));
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

.charts-section h2 {
    margin: 0 0 2rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    text-align: center;
    position: relative;
}

.charts-section h2::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    border-radius: 2px;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.chart-container {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
    text-align: center;
}

.chart-container h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.chart-container h3::before {
    content: "📊";
    font-size: 1.1rem;
}

canvas {
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    height: auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .teams-grid {
        grid-template-columns: 1fr;
        padding: 0.5rem;
    }

    .team-detail {
        padding: 0;
        margin: 0 0.5rem;
    }

    .team-detail-header {
        padding: 1rem;
    }

    .team-basic-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;
    }

    .team-detail-logo {
        width: 120px;
        height: 120px;
        margin: 0 auto;
    }

    .team-detail-info {
        min-width: auto;
        width: 100%;
    }

    .team-detail-info h2 {
        font-size: 1.8rem;
    }

    .team-meta {
        grid-template-columns: 1fr;
    }

    .team-meta-item {
        text-align: center;
    }

    /* 多媒体响应式 */
    .media-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .chart-container canvas {
        width: 100%;
        height: auto;
    }

    .players-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .team-stats {
        grid-template-columns: repeat(2, 1fr);
        padding: 1rem;
    }

    .team-stat-card {
        padding: 1rem 0.5rem;
    }

    .team-stat-card .stat-value {
        font-size: 1.5rem;
    }

    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: none;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-actions {
        justify-content: center;
    }

    .filter-content {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .filter-group {
        padding: 0.5rem;
    }

    .filter-group legend {
        font-size: 0.8rem;
        padding: 0.2rem 0.4rem;
    }

    .filter-group label {
        font-size: 0.75rem;
        margin-bottom: 0.2rem;
    }

    .filter-select {
        padding: 0.4rem;
        font-size: 0.8rem;
        height: 36px;
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
        height: 36px;
    }

    .filter-actions {
        gap: 0.5rem;
        margin-top: 0.5rem;
        padding-top: 0.5rem;
    }
}

@media (max-width: 480px) {
    .team-stats {
        grid-template-columns: 1fr;
    }

    .action-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .page-header h1 {
        font-size: 1.2rem;
    }
}