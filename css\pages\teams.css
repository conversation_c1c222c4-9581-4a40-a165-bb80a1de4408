/* 球队页面特定样式 */

/* 球队卡片网格 */
.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

/* 球队卡片 */
.team-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease;
}

.team-card:hover {
    transform: translateY(-5px);
}

.team-card .card-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.team-card .card-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.category-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
}

.team-card .card-body {
    padding: 1rem;
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
}

.team-logo {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.team-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.team-info p {
    margin: 0.5rem 0;
    color: var(--text-secondary);
}

.team-info strong {
    color: var(--text-primary);
}

.team-card .card-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

.view-btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;
}

.view-btn:hover {
    background: var(--primary-dark);
}

/* 球队详情 */
.team-detail {
    padding: 2rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.team-detail-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.team-detail-logo {
    width: 150px;
    height: 150px;
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.team-detail-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.team-detail-info h2 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
}

.team-meta p {
    margin: 0.5rem 0;
    color: var(--text-secondary);
}

.team-description {
    margin-top: 1rem;
    color: var(--text-secondary);
}

.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.team-stat-card {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.team-stat-card .stat-label {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.team-stat-card .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* 球员列表 */
.team-players,
.team-matches {
    margin-top: 2rem;
}

.team-players h3,
.team-matches h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.players-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.player-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.player-card h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.player-info p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* 比赛列表 */
.matches-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.match-item {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.match-item:hover {
    background: var(--bg-hover);
}

.match-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.match-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.team-name {
    flex: 1;
    font-weight: 500;
    color: var(--text-primary);
}

.team-name.home {
    text-align: right;
    padding-right: 1rem;
}

.team-name.away {
    text-align: left;
    padding-left: 1rem;
}

.match-score {
    padding: 0.25rem 0.5rem;
    background: rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-sm);
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

.result {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: bold;
}

.result.win {
    background: #4caf50;
    color: white;
}

.result.draw {
    background: #ff9800;
    color: white;
}

.result.loss {
    background: #f44336;
    color: white;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.back-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.back-btn:hover {
    background: var(--secondary-dark);
}

.no-data {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .teams-grid {
        grid-template-columns: 1fr;
    }

    .team-detail {
        padding: 1rem;
    }

    .team-detail-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .players-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}