<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="中国青少年足球数据平台 - 球队信息管理系统，使用HTML5技术构建">
    <meta name="keywords" content="青少年足球,球队管理,足球数据,青训,HTML5">
    <meta name="author" content="中国青少年足球数据平台">

    <!-- HTML5 PWA支持 -->
    <meta name="theme-color" content="#2ecc71">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="足球数据平台">

    <title>球队管理 - 中国青少年足球数据平台</title>

    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css">
    <!-- 页面特定样式 -->
    <link rel="stylesheet" href="css/pages/teams.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- HTML5 预加载关键资源 -->
    <link rel="preload" href="js/data.js" as="script">
    <link rel="preload" href="js/app.js" as="script">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style">

    <!-- HTML5 PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- 图标 -->
    <link rel="icon" type="image/png" sizes="32x32" href="images/logo.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/logo.png">
    <link rel="apple-touch-icon" href="images/logo.png">
</head>

<body>
    <!-- 页面加载指示器 -->
    <div id="loading-indicator" class="loading-indicator" style="display: none;">
        <div class="spinner"></div>
        <p>加载中...</p>
    </div>

    <header role="banner"></header>

    <!-- HTML5 语义化主内容区域 -->
    <main role="main">
        <!-- HTML5 语义化页面标题区域 -->
        <header class="page-header">
            <hgroup>
                <h1><i class="fas fa-users" aria-hidden="true"></i> 球队管理中心</h1>
                <p class="page-subtitle">使用HTML5技术构建的现代化球队信息管理系统</p>
            </hgroup>
            <nav class="header-actions" aria-label="页面操作">
                <button id="location-btn" class="action-btn" title="HTML5地理位置API" aria-label="获取当前位置">
                    <i class="fas fa-map-marker-alt"></i>
                    <span class="btn-text">位置</span>
                </button>
                <button id="share-btn" class="action-btn" title="HTML5分享API" aria-label="分享当前页面">
                    <i class="fas fa-share-alt"></i>
                    <span class="btn-text">分享</span>
                </button>
                <button id="fullscreen-btn" class="action-btn" title="HTML5全屏API" aria-label="切换全屏模式">
                    <i class="fas fa-expand"></i>
                    <span class="btn-text">全屏</span>
                </button>
                <button id="storage-btn" class="action-btn" title="HTML5本地存储" aria-label="存储管理">
                    <i class="fas fa-database"></i>
                    <span class="btn-text">存储</span>
                </button>
            </nav>
        </header>

        <!-- HTML5 语义化搜索和筛选区域 -->
        <section class="search-filter-section" role="search" aria-labelledby="search-heading">
            <h2 id="search-heading" class="visually-hidden">搜索和筛选</h2>

            <!-- HTML5 搜索容器 -->
            <div id="search-container" class="search-container"></div>

            <!-- HTML5 details/summary 元素实现可折叠筛选器 -->
            <details class="filter-section" open>
                <summary class="filter-toggle">
                    <i class="fas fa-filter"></i> 高级筛选选项
                    <small>(HTML5 details/summary 元素)</small>
                </summary>
                <form class="filter-content" id="filter-form">
                    <fieldset class="filter-group">
                        <legend>年龄组别</legend>
                        <label for="team-filter">选择年龄组:</label>
                        <select id="team-filter" class="filter-select" required>
                            <option value="all">所有类别</option>
                            <option value="u12">U12 (12岁以下)</option>
                            <option value="u15">U15 (15岁以下)</option>
                            <option value="u17">U17 (17岁以下)</option>
                            <option value="u20">U20 (20岁以下)</option>
                        </select>
                    </fieldset>

                    <fieldset class="filter-group">
                        <legend>地理区域</legend>
                        <label for="region-filter">选择地区:</label>
                        <select id="region-filter" class="filter-select">
                            <option value="all">所有地区</option>
                            <option value="北京">北京市</option>
                            <option value="上海">上海市</option>
                            <option value="广东">广东省</option>
                            <option value="山东">山东省</option>
                            <option value="辽宁">辽宁省</option>
                            <option value="河南">河南省</option>
                            <option value="湖北">湖北省</option>
                            <option value="天津">天津市</option>
                            <option value="重庆">重庆市</option>
                        </select>
                    </fieldset>

                    <fieldset class="filter-group">
                        <legend>排序方式</legend>
                        <label for="sort-select">排序依据:</label>
                        <select id="sort-select" class="filter-select">
                            <option value="name">按名称排序</option>
                            <option value="founded">按成立时间</option>
                            <option value="region">按地区排序</option>
                        </select>
                    </fieldset>

                    <div class="filter-actions">
                        <button type="button" id="apply-filters" class="btn btn-primary">
                            <i class="fas fa-search"></i> 应用筛选
                        </button>
                        <button type="reset" id="reset-filters" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </form>
            </details>
        </section>

        <!-- HTML5 语义化球队列表区域 -->
        <section class="teams-section" role="region" aria-labelledby="teams-heading">
            <header class="section-header">
                <h2 id="teams-heading">球队列表</h2>
                <div id="filter-stats" class="filter-stats" aria-live="polite">
                    <!-- 筛选统计信息将在这里显示 -->
                </div>
            </header>

            <!-- HTML5 语义化球队网格容器 -->
            <div id="teams-container" class="teams-grid" role="grid" aria-label="球队信息网格">
                <!-- 球队卡片将由JavaScript动态加载 -->
            </div>

            <!-- HTML5 语义化无数据状态 -->
            <aside id="no-teams-message" class="no-data" style="display: none;" role="status" aria-live="polite">
                <i class="fas fa-users-slash" aria-hidden="true"></i>
                <h3>暂无球队数据</h3>
                <p>当前筛选条件下没有找到匹配的球队信息</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> 刷新页面
                </button>
            </aside>
        </section>

        <!-- HTML5 语义化球队详情区域 -->
        <article id="team-detail" class="team-detail" style="display: none;" role="main"
            aria-labelledby="team-detail-heading">
            <!-- 球队详细信息将由JavaScript动态加载 -->
        </article>

        <!-- HTML5 多媒体展示区域 -->
        <aside class="multimedia-section" id="multimedia-section" style="display: none;">
            <h2>多媒体内容</h2>
            <div class="media-grid">
                <!-- HTML5 视频元素 -->
                <section class="video-section">
                    <h3>球队训练视频</h3>
                    <video id="team-video" controls preload="metadata" poster="images/video-poster.jpg">
                        <source src="videos/team-training.mp4" type="video/mp4">
                        <source src="videos/team-training.webm" type="video/webm">
                        <track kind="subtitles" src="videos/subtitles-zh.vtt" srclang="zh" label="中文字幕">
                        您的浏览器不支持HTML5视频播放。
                    </video>
                </section>

                <!-- HTML5 音频元素 -->
                <section class="audio-section">
                    <h3>球队队歌</h3>
                    <audio id="team-audio" controls preload="none">
                        <source src="audio/team-anthem.mp3" type="audio/mpeg">
                        <source src="audio/team-anthem.ogg" type="audio/ogg">
                        您的浏览器不支持HTML5音频播放。
                    </audio>
                </section>
            </div>
        </aside>

        <!-- HTML5 Canvas 图表区域 -->
        <aside class="charts-section" id="charts-section" style="display: none;">
            <h2>数据可视化图表</h2>
            <div class="charts-grid">
                <section class="chart-container">
                    <h3>球队统计图表</h3>
                    <canvas id="team-stats-chart" width="400" height="300" aria-label="球队统计数据图表">
                        您的浏览器不支持HTML5 Canvas。
                    </canvas>
                </section>

                <section class="chart-container">
                    <h3>地区分布图</h3>
                    <canvas id="region-chart" width="400" height="300" aria-label="球队地区分布图表">
                        您的浏览器不支持HTML5 Canvas。
                    </canvas>
                </section>
            </div>
        </aside>
    </main>

    <footer role="contentinfo"></footer>

    <!-- 组件 -->
    <script src="components/Navigation.js"></script>
    <script src="components/SearchBar.js"></script>
    <script src="components/Card.js"></script>
    <!-- 数据和应用逻辑 -->
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>

    <script>
        // HTML5 功能增强
        class TeamsPageEnhancer {
            constructor() {
                this.geolocation = null;
                this.isFullscreen = false;
                this.init();
            }

            init () {
                this.initGeolocation();
                this.initShare();
                this.initFullscreen();
                this.initOfflineSupport();
                this.initPerformanceMonitoring();
            }

            // 地理位置功能
            initGeolocation () {
                const locationBtn = document.getElementById('location-btn');
                if (locationBtn && 'geolocation' in navigator) {
                    locationBtn.addEventListener('click', () => {
                        this.showLoadingIndicator();
                        navigator.geolocation.getCurrentPosition(
                            (position) => {
                                this.hideLoadingIndicator();
                                const { latitude, longitude } = position.coords;
                                this.geolocation = { latitude, longitude };
                                this.showLocationInfo(latitude, longitude);
                                // 保存位置信息到本地存储
                                localStorage.setItem('userLocation', JSON.stringify(this.geolocation));
                            },
                            (error) => {
                                this.hideLoadingIndicator();
                                this.showNotification('无法获取位置信息: ' + error.message, 'error');
                            },
                            { enableHighAccuracy: true, timeout: 10000, maximumAge: 300000 }
                        );
                    });
                } else {
                    if (locationBtn) locationBtn.style.display = 'none';
                }
            }

            // 分享功能
            initShare () {
                const shareBtn = document.getElementById('share-btn');
                if (shareBtn) {
                    shareBtn.addEventListener('click', async () => {
                        const shareData = {
                            title: '中国青少年足球数据平台 - 球队',
                            text: '查看青少年足球球队信息',
                            url: window.location.href
                        };

                        if (navigator.share) {
                            try {
                                await navigator.share(shareData);
                            } catch (err) {
                                this.fallbackShare(shareData);
                            }
                        } else {
                            this.fallbackShare(shareData);
                        }
                    });
                }
            }

            // 全屏功能
            initFullscreen () {
                const fullscreenBtn = document.getElementById('fullscreen-btn');
                if (fullscreenBtn) {
                    fullscreenBtn.addEventListener('click', () => {
                        if (!this.isFullscreen) {
                            this.enterFullscreen();
                        } else {
                            this.exitFullscreen();
                        }
                    });

                    // 监听全屏状态变化
                    document.addEventListener('fullscreenchange', () => {
                        this.isFullscreen = !!document.fullscreenElement;
                        this.updateFullscreenButton();
                    });
                }
            }

            // 离线支持
            initOfflineSupport () {
                window.addEventListener('online', () => {
                    this.showNotification('网络连接已恢复', 'success');
                });

                window.addEventListener('offline', () => {
                    this.showNotification('网络连接已断开，正在使用离线模式', 'warning');
                });
            }

            // 性能监控
            initPerformanceMonitoring () {
                if ('performance' in window) {
                    window.addEventListener('load', () => {
                        setTimeout(() => {
                            const perfData = performance.getEntriesByType('navigation')[0];
                            console.log('页面加载性能:', {
                                loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                                domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                                totalTime: perfData.loadEventEnd - perfData.fetchStart
                            });
                        }, 0);
                    });
                }
            }

            showLoadingIndicator () {
                document.getElementById('loading-indicator').style.display = 'flex';
            }

            hideLoadingIndicator () {
                document.getElementById('loading-indicator').style.display = 'none';
            }

            showLocationInfo (lat, lng) {
                this.showNotification(`当前位置: ${lat.toFixed(4)}, ${lng.toFixed(4)}`, 'info');
            }

            fallbackShare (shareData) {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(shareData.url).then(() => {
                        this.showNotification('链接已复制到剪贴板', 'success');
                    });
                } else {
                    // 创建临时输入框复制链接
                    const tempInput = document.createElement('input');
                    tempInput.value = shareData.url;
                    document.body.appendChild(tempInput);
                    tempInput.select();
                    document.execCommand('copy');
                    document.body.removeChild(tempInput);
                    this.showNotification('链接已复制到剪贴板', 'success');
                }
            }

            enterFullscreen () {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                }
            }

            exitFullscreen () {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }

            updateFullscreenButton () {
                const btn = document.getElementById('fullscreen-btn');
                const icon = btn.querySelector('i');
                if (this.isFullscreen) {
                    icon.className = 'fas fa-compress';
                    btn.title = '退出全屏';
                } else {
                    icon.className = 'fas fa-expand';
                    btn.title = '全屏模式';
                }
            }

            showNotification (message, type = 'info') {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                    <span>${message}</span>
                    <button class="notification-close">&times;</button>
                `;

                // 添加到页面
                document.body.appendChild(notification);

                // 显示动画
                setTimeout(() => notification.classList.add('show'), 100);

                // 自动隐藏
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);

                // 点击关闭
                notification.querySelector('.notification-close').addEventListener('click', () => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                });
            }

            getNotificationIcon (type) {
                const icons = {
                    success: 'check-circle',
                    error: 'exclamation-circle',
                    warning: 'exclamation-triangle',
                    info: 'info-circle'
                };
                return icons[type] || 'info-circle';
            }
        }

        // 初始化组件
        document.addEventListener('DOMContentLoaded', function () {
            // 初始化HTML5功能增强
            const enhancer = new TeamsPageEnhancer();

            // 初始化导航
            Navigation.init();

            // 初始化搜索栏
            document.getElementById('search-container').innerHTML = SearchBar.render('team-search', '搜索球队...');
            SearchBar.init('team-search', searchTeams);

            // 初始化球队筛选
            initTeamFilter();
            initAdvancedFilters();

            // 检查是否有球队ID参数
            const urlParams = new URLSearchParams(window.location.search);
            const teamId = urlParams.get('id');

            if (teamId) {
                // 显示球队详情
                showTeamDetail(teamId);
            } else {
                // 加载球队列表
                loadTeams();
            }
        });

        // 高级筛选功能
        function initAdvancedFilters () {
            const regionFilter = document.getElementById('region-filter');
            const sortSelect = document.getElementById('sort-select');

            if (regionFilter) {
                regionFilter.addEventListener('change', applyFilters);
            }

            if (sortSelect) {
                sortSelect.addEventListener('change', applyFilters);
            }
        }

        function applyFilters () {
            const categoryFilter = document.getElementById('team-filter').value;
            const regionFilter = document.getElementById('region-filter').value;
            const sortBy = document.getElementById('sort-select').value;
            const searchTerm = document.getElementById('team-search')?.value || '';

            filterTeams(categoryFilter, searchTerm, regionFilter, sortBy);
        }
    </script>
</body>

</html>