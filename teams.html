<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="中国青少年足球数据平台 - 球队信息管理系统，使用HTML5技术构建">
    <meta name="keywords" content="青少年足球,球队管理,足球数据,青训,HTML5">
    <meta name="author" content="中国青少年足球数据平台">

    <!-- HTML5 PWA支持 -->
    <meta name="theme-color" content="#2ecc71">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="足球数据平台">

    <title>球队管理 - 中国青少年足球数据平台</title>

    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css">
    <!-- 页面特定样式 -->
    <link rel="stylesheet" href="css/pages/teams.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- HTML5 预加载关键资源 -->
    <link rel="preload" href="js/data.js" as="script">
    <link rel="preload" href="js/app.js" as="script">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style">

    <!-- HTML5 PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- 图标 -->
    <link rel="icon" type="image/png" sizes="32x32" href="images/logo.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/logo.png">
    <link rel="apple-touch-icon" href="images/logo.png">
</head>

<body>
    <!-- 页面加载指示器 -->
    <div id="loading-indicator" class="loading-indicator" style="display: none;">
        <div class="spinner"></div>
        <p>加载中...</p>
    </div>

    <header role="banner"></header>

    <!-- HTML5 语义化主内容区域 -->
    <main role="main">
        <!-- HTML5 语义化页面标题区域 -->
        <header class="page-header">
            <hgroup>
                <h1><i class="fas fa-users" aria-hidden="true"></i> 球队管理</h1>
                <p class="page-subtitle">现代化球队信息管理系统</p>
            </hgroup>
        </header>

        <!-- HTML5 语义化搜索和筛选区域 -->
        <section class="search-filter-section" role="search" aria-labelledby="search-heading">
            <h2 id="search-heading" class="visually-hidden">搜索和筛选</h2>

            <!-- HTML5 搜索容器 -->
            <div id="search-container" class="search-container"></div>

            <!-- HTML5 details/summary 元素实现可折叠筛选器 -->
            <details class="filter-section" open>
                <summary class="filter-toggle">
                    <i class="fas fa-filter"></i> 高级筛选选项
                    <small>(HTML5 details/summary 元素)</small>
                </summary>
                <form class="filter-content" id="filter-form">
                    <fieldset class="filter-group">
                        <legend>年龄组别</legend>
                        <label for="team-filter">选择年龄组:</label>
                        <select id="team-filter" class="filter-select" required>
                            <option value="all">所有类别</option>
                            <option value="u12">U12 (12岁以下)</option>
                            <option value="u15">U15 (15岁以下)</option>
                            <option value="u17">U17 (17岁以下)</option>
                            <option value="u20">U20 (20岁以下)</option>
                        </select>
                    </fieldset>

                    <fieldset class="filter-group">
                        <legend>地理区域</legend>
                        <label for="region-filter">选择地区:</label>
                        <select id="region-filter" class="filter-select">
                            <option value="all">所有地区</option>
                            <option value="北京">北京市</option>
                            <option value="上海">上海市</option>
                            <option value="广东">广东省</option>
                            <option value="山东">山东省</option>
                            <option value="辽宁">辽宁省</option>
                            <option value="河南">河南省</option>
                            <option value="湖北">湖北省</option>
                            <option value="天津">天津市</option>
                            <option value="重庆">重庆市</option>
                        </select>
                    </fieldset>

                    <fieldset class="filter-group">
                        <legend>排序方式</legend>
                        <label for="sort-select">排序依据:</label>
                        <select id="sort-select" class="filter-select">
                            <option value="name">按名称排序</option>
                            <option value="founded">按成立时间</option>
                            <option value="region">按地区排序</option>
                        </select>
                    </fieldset>

                    <div class="filter-actions">
                        <button type="button" id="apply-filters" class="btn btn-primary">
                            <i class="fas fa-search"></i> 应用筛选
                        </button>
                        <button type="reset" id="reset-filters" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </form>
            </details>
        </section>

        <!-- HTML5 语义化球队列表区域 -->
        <section class="teams-section" role="region" aria-labelledby="teams-heading">
            <header class="section-header">
                <h2 id="teams-heading">球队列表</h2>
                <div id="filter-stats" class="filter-stats" aria-live="polite">
                    <!-- 筛选统计信息将在这里显示 -->
                </div>
            </header>

            <!-- HTML5 语义化球队网格容器 -->
            <div id="teams-container" class="teams-grid" role="grid" aria-label="球队信息网格">
                <!-- 球队卡片将由JavaScript动态加载 -->
            </div>

            <!-- HTML5 语义化无数据状态 -->
            <aside id="no-teams-message" class="no-data" style="display: none;" role="status" aria-live="polite">
                <i class="fas fa-users-slash" aria-hidden="true"></i>
                <h3>暂无球队数据</h3>
                <p>当前筛选条件下没有找到匹配的球队信息</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> 刷新页面
                </button>
            </aside>
        </section>

        <!-- HTML5 语义化球队详情区域 -->
        <article id="team-detail" class="team-detail" style="display: none;" role="main"
            aria-labelledby="team-detail-heading">
            <!-- 球队详细信息将由JavaScript动态加载 -->
        </article>


    </main>

    <footer role="contentinfo"></footer>

    <!-- 组件 -->
    <script src="components/Navigation.js"></script>
    <script src="components/SearchBar.js"></script>
    <script src="components/Card.js"></script>
    <!-- 数据和应用逻辑 -->
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 简化的HTML5功能增强
        class TeamsPageEnhancer {
            constructor() {
                this.init();
            }

            init () {
                console.log('🚀 初始化球队页面功能...');
                this.initLocalStorage();
                this.initOfflineSupport();
            }

            // HTML5本地存储功能
            initLocalStorage () {
                // 保存用户偏好设置
                const saveUserPreferences = () => {
                    const preferences = {
                        lastVisited: new Date().toISOString(),
                        favoriteTeams: JSON.parse(localStorage.getItem('favoriteTeams') || '[]'),
                        viewMode: localStorage.getItem('viewMode') || 'grid'
                    };
                    localStorage.setItem('userPreferences', JSON.stringify(preferences));
                };

                // 页面卸载时保存偏好
                window.addEventListener('beforeunload', saveUserPreferences);

                // 恢复用户偏好
                const preferences = JSON.parse(localStorage.getItem('userPreferences') || '{}');
                if (preferences.lastVisited) {
                    console.log('上次访问时间:', preferences.lastVisited);
                }
            }

            // 离线支持
            initOfflineSupport () {
                window.addEventListener('online', () => {
                    console.log('网络连接已恢复');
                });

                window.addEventListener('offline', () => {
                    console.log('网络连接已断开，正在使用离线模式');
                });
            }
        }

        // 初始化组件
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🚀 初始化球队页面...');

            // 初始化HTML5功能增强
            const enhancer = new TeamsPageEnhancer();

            // 初始化统一导航
            Navigation.init();

            // 初始化搜索栏
            document.getElementById('search-container').innerHTML = SearchBar.render('team-search', '搜索球队...');
            SearchBar.init('team-search', searchTeams);

            // 初始化球队筛选
            initTeamFilter();
            initAdvancedFilters();

            // 检查是否有球队ID参数
            const urlParams = new URLSearchParams(window.location.search);
            const teamId = urlParams.get('id');

            if (teamId) {
                // 显示球队详情
                showTeamDetail(teamId);
            } else {
                // 加载球队列表
                loadTeams();
            }
        });

        // 高级筛选功能
        function initAdvancedFilters () {
            const regionFilter = document.getElementById('region-filter');
            const sortSelect = document.getElementById('sort-select');
            const applyBtn = document.getElementById('apply-filters');
            const resetBtn = document.getElementById('reset-filters');

            if (regionFilter) {
                regionFilter.addEventListener('change', applyFilters);
            }

            if (sortSelect) {
                sortSelect.addEventListener('change', applyFilters);
            }

            if (applyBtn) {
                applyBtn.addEventListener('click', applyFilters);
            }

            if (resetBtn) {
                resetBtn.addEventListener('click', resetFilters);
            }
        }

        function applyFilters () {
            const categoryFilter = document.getElementById('team-filter').value;
            const regionFilter = document.getElementById('region-filter').value;
            const sortBy = document.getElementById('sort-select').value;
            const searchTerm = document.getElementById('team-search')?.value || '';

            filterTeams(categoryFilter, searchTerm, regionFilter, sortBy);
        }
    </script>
</body>

</html>