<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>球队 - 中国青少年足球数据平台</title>
    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css">
    <!-- 页面特定样式 -->
    <link rel="stylesheet" href="css/pages/teams.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>

<body>
    <header></header>

    <main>
        <div class="page-header">
            <h2><i class="fas fa-users"></i> 球队</h2>
        </div>

        <!-- 搜索栏 -->
        <div id="search-container"></div>

        <!-- 筛选器 -->
        <div class="filter-section">
            <select id="team-filter" class="filter-select">
                <option value="all">所有类别</option>
                <option value="u12">U12</option>
                <option value="u15">U15</option>
                <option value="u17">U17</option>
                <option value="u20">U20</option>
            </select>
        </div>

        <!-- 球队列表 -->
        <div id="teams-container" class="teams-grid">
            <!-- 球队卡片将由JavaScript动态加载 -->
        </div>

        <!-- 球队详情 -->
        <div id="team-detail" class="team-detail" style="display: none;">
            <!-- 球队详细信息将由JavaScript动态加载 -->
        </div>
    </main>

    <footer></footer>

    <!-- 组件 -->
    <script src="components/Navigation.js"></script>
    <script src="components/SearchBar.js"></script>
    <script src="components/Card.js"></script>
    <!-- 数据和应用逻辑 -->
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>
    <script>
        // 初始化组件
        document.addEventListener('DOMContentLoaded', function () {
            // 初始化导航
            Navigation.init();

            // 初始化搜索栏
            document.getElementById('search-container').innerHTML = SearchBar.render('team-search', '搜索球队...');
            SearchBar.init('team-search', searchTeams);

            // 初始化球队筛选
            initTeamFilter();

            // 检查是否有球队ID参数
            const urlParams = new URLSearchParams(window.location.search);
            const teamId = urlParams.get('id');

            if (teamId) {
                // 显示球队详情
                showTeamDetail(teamId);
            } else {
                // 加载球队列表
                loadTeams();
            }
        });
    </script>
</body>

</html>