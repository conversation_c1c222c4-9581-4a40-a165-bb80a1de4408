<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="中国青少年足球数据平台 - 球队信息管理系统">
    <meta name="keywords" content="青少年足球,球队管理,足球数据,青训">
    <meta name="author" content="中国青少年足球数据平台">

    <!-- PWA支持 -->
    <meta name="theme-color" content="#2ecc71">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="足球数据平台">

    <title>球队 - 中国青少年足球数据平台</title>

    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css">
    <!-- 页面特定样式 -->
    <link rel="stylesheet" href="css/pages/teams.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="js/data.js" as="script">
    <link rel="preload" href="js/app.js" as="script">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- 图标 -->
    <link rel="icon" type="image/png" sizes="32x32" href="images/logo.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/logo.png">
    <link rel="apple-touch-icon" href="images/logo.png">
</head>

<body>
    <!-- 页面加载指示器 -->
    <div id="loading-indicator" class="loading-indicator" style="display: none;">
        <div class="spinner"></div>
        <p>加载中...</p>
    </div>

    <header role="banner"></header>

    <main role="main">
        <!-- 页面标题区域 -->
        <section class="page-header" role="banner">
            <h1><i class="fas fa-users" aria-hidden="true"></i> 球队管理</h1>
            <div class="header-actions">
                <button id="location-btn" class="action-btn" title="获取位置信息" aria-label="获取当前位置">
                    <i class="fas fa-map-marker-alt"></i>
                </button>
                <button id="share-btn" class="action-btn" title="分享页面" aria-label="分享当前页面">
                    <i class="fas fa-share-alt"></i>
                </button>
                <button id="fullscreen-btn" class="action-btn" title="全屏模式" aria-label="切换全屏模式">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </section>

        <!-- 搜索和筛选区域 -->
        <section class="search-filter-section" role="search">
            <div id="search-container" class="search-container"></div>

            <!-- 高级筛选器 -->
            <details class="filter-section">
                <summary class="filter-toggle">
                    <i class="fas fa-filter"></i> 筛选选项
                </summary>
                <div class="filter-content">
                    <div class="filter-group">
                        <label for="team-filter">年龄组别:</label>
                        <select id="team-filter" class="filter-select">
                            <option value="all">所有类别</option>
                            <option value="u12">U12</option>
                            <option value="u15">U15</option>
                            <option value="u17">U17</option>
                            <option value="u20">U20</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="region-filter">地区:</label>
                        <select id="region-filter" class="filter-select">
                            <option value="all">所有地区</option>
                            <option value="北京">北京</option>
                            <option value="上海">上海</option>
                            <option value="广东">广东</option>
                            <option value="山东">山东</option>
                            <option value="辽宁">辽宁</option>
                            <option value="河南">河南</option>
                            <option value="湖北">湖北</option>
                            <option value="天津">天津</option>
                            <option value="重庆">重庆</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="sort-select">排序方式:</label>
                        <select id="sort-select" class="filter-select">
                            <option value="name">按名称</option>
                            <option value="founded">按成立时间</option>
                            <option value="region">按地区</option>
                        </select>
                    </div>
                </div>
            </details>
        </section>

        <!-- 球队列表区域 -->
        <section class="teams-section" role="region" aria-label="球队列表">
            <div id="teams-container" class="teams-grid">
                <!-- 球队卡片将由JavaScript动态加载 -->
            </div>

            <!-- 无数据提示 -->
            <div id="no-teams-message" class="no-data" style="display: none;">
                <i class="fas fa-users-slash"></i>
                <p>暂无球队数据</p>
                <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
            </div>
        </section>

        <!-- 球队详情区域 -->
        <section id="team-detail" class="team-detail" style="display: none;" role="region" aria-label="球队详情">
            <!-- 球队详细信息将由JavaScript动态加载 -->
        </section>
    </main>

    <footer role="contentinfo"></footer>

    <!-- 组件 -->
    <script src="components/Navigation.js"></script>
    <script src="components/SearchBar.js"></script>
    <script src="components/Card.js"></script>
    <!-- 数据和应用逻辑 -->
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>

    <script>
        // HTML5 功能增强
        class TeamsPageEnhancer {
            constructor() {
                this.geolocation = null;
                this.isFullscreen = false;
                this.init();
            }

            init () {
                this.initGeolocation();
                this.initShare();
                this.initFullscreen();
                this.initOfflineSupport();
                this.initPerformanceMonitoring();
            }

            // 地理位置功能
            initGeolocation () {
                const locationBtn = document.getElementById('location-btn');
                if (locationBtn && 'geolocation' in navigator) {
                    locationBtn.addEventListener('click', () => {
                        this.showLoadingIndicator();
                        navigator.geolocation.getCurrentPosition(
                            (position) => {
                                this.hideLoadingIndicator();
                                const { latitude, longitude } = position.coords;
                                this.geolocation = { latitude, longitude };
                                this.showLocationInfo(latitude, longitude);
                                // 保存位置信息到本地存储
                                localStorage.setItem('userLocation', JSON.stringify(this.geolocation));
                            },
                            (error) => {
                                this.hideLoadingIndicator();
                                this.showNotification('无法获取位置信息: ' + error.message, 'error');
                            },
                            { enableHighAccuracy: true, timeout: 10000, maximumAge: 300000 }
                        );
                    });
                } else {
                    if (locationBtn) locationBtn.style.display = 'none';
                }
            }

            // 分享功能
            initShare () {
                const shareBtn = document.getElementById('share-btn');
                if (shareBtn) {
                    shareBtn.addEventListener('click', async () => {
                        const shareData = {
                            title: '中国青少年足球数据平台 - 球队',
                            text: '查看青少年足球球队信息',
                            url: window.location.href
                        };

                        if (navigator.share) {
                            try {
                                await navigator.share(shareData);
                            } catch (err) {
                                this.fallbackShare(shareData);
                            }
                        } else {
                            this.fallbackShare(shareData);
                        }
                    });
                }
            }

            // 全屏功能
            initFullscreen () {
                const fullscreenBtn = document.getElementById('fullscreen-btn');
                if (fullscreenBtn) {
                    fullscreenBtn.addEventListener('click', () => {
                        if (!this.isFullscreen) {
                            this.enterFullscreen();
                        } else {
                            this.exitFullscreen();
                        }
                    });

                    // 监听全屏状态变化
                    document.addEventListener('fullscreenchange', () => {
                        this.isFullscreen = !!document.fullscreenElement;
                        this.updateFullscreenButton();
                    });
                }
            }

            // 离线支持
            initOfflineSupport () {
                window.addEventListener('online', () => {
                    this.showNotification('网络连接已恢复', 'success');
                });

                window.addEventListener('offline', () => {
                    this.showNotification('网络连接已断开，正在使用离线模式', 'warning');
                });
            }

            // 性能监控
            initPerformanceMonitoring () {
                if ('performance' in window) {
                    window.addEventListener('load', () => {
                        setTimeout(() => {
                            const perfData = performance.getEntriesByType('navigation')[0];
                            console.log('页面加载性能:', {
                                loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                                domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                                totalTime: perfData.loadEventEnd - perfData.fetchStart
                            });
                        }, 0);
                    });
                }
            }

            showLoadingIndicator () {
                document.getElementById('loading-indicator').style.display = 'flex';
            }

            hideLoadingIndicator () {
                document.getElementById('loading-indicator').style.display = 'none';
            }

            showLocationInfo (lat, lng) {
                this.showNotification(`当前位置: ${lat.toFixed(4)}, ${lng.toFixed(4)}`, 'info');
            }

            fallbackShare (shareData) {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(shareData.url).then(() => {
                        this.showNotification('链接已复制到剪贴板', 'success');
                    });
                } else {
                    // 创建临时输入框复制链接
                    const tempInput = document.createElement('input');
                    tempInput.value = shareData.url;
                    document.body.appendChild(tempInput);
                    tempInput.select();
                    document.execCommand('copy');
                    document.body.removeChild(tempInput);
                    this.showNotification('链接已复制到剪贴板', 'success');
                }
            }

            enterFullscreen () {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                }
            }

            exitFullscreen () {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }

            updateFullscreenButton () {
                const btn = document.getElementById('fullscreen-btn');
                const icon = btn.querySelector('i');
                if (this.isFullscreen) {
                    icon.className = 'fas fa-compress';
                    btn.title = '退出全屏';
                } else {
                    icon.className = 'fas fa-expand';
                    btn.title = '全屏模式';
                }
            }

            showNotification (message, type = 'info') {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                    <span>${message}</span>
                    <button class="notification-close">&times;</button>
                `;

                // 添加到页面
                document.body.appendChild(notification);

                // 显示动画
                setTimeout(() => notification.classList.add('show'), 100);

                // 自动隐藏
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);

                // 点击关闭
                notification.querySelector('.notification-close').addEventListener('click', () => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                });
            }

            getNotificationIcon (type) {
                const icons = {
                    success: 'check-circle',
                    error: 'exclamation-circle',
                    warning: 'exclamation-triangle',
                    info: 'info-circle'
                };
                return icons[type] || 'info-circle';
            }
        }

        // 初始化组件
        document.addEventListener('DOMContentLoaded', function () {
            // 初始化HTML5功能增强
            const enhancer = new TeamsPageEnhancer();

            // 初始化导航
            Navigation.init();

            // 初始化搜索栏
            document.getElementById('search-container').innerHTML = SearchBar.render('team-search', '搜索球队...');
            SearchBar.init('team-search', searchTeams);

            // 初始化球队筛选
            initTeamFilter();
            initAdvancedFilters();

            // 检查是否有球队ID参数
            const urlParams = new URLSearchParams(window.location.search);
            const teamId = urlParams.get('id');

            if (teamId) {
                // 显示球队详情
                showTeamDetail(teamId);
            } else {
                // 加载球队列表
                loadTeams();
            }
        });

        // 高级筛选功能
        function initAdvancedFilters () {
            const regionFilter = document.getElementById('region-filter');
            const sortSelect = document.getElementById('sort-select');

            if (regionFilter) {
                regionFilter.addEventListener('change', applyFilters);
            }

            if (sortSelect) {
                sortSelect.addEventListener('change', applyFilters);
            }
        }

        function applyFilters () {
            const categoryFilter = document.getElementById('team-filter').value;
            const regionFilter = document.getElementById('region-filter').value;
            const sortBy = document.getElementById('sort-select').value;
            const searchTerm = document.getElementById('team-search')?.value || '';

            filterTeams(categoryFilter, searchTerm, regionFilter, sortBy);
        }
    </script>
</body>

</html>