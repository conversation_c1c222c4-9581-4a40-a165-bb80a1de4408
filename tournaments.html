<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赛事列表 - 青少年足球联赛</title>
    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css">
    <!-- 页面特定样式 -->
    <link rel="stylesheet" href="css/pages/tournaments.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- 导航栏由Navigation组件填充 -->
    <header></header>

    <main>
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>赛事列表</h1>
        </div>

        <!-- 搜索栏由SearchBar组件填充 -->
        <div class="search-container"></div>

        <!-- 赛事过滤器 -->
        <div class="filter-section">
            <div class="filter-group">
                <button class="filter-btn active" data-category="all">全部</button>
                <button class="filter-btn" data-category="U12">U12</button>
                <button class="filter-btn" data-category="U15">U15</button>
                <button class="filter-btn" data-category="U17">U17</button>
                <button class="filter-btn" data-category="U20">U20</button>
            </div>
        </div>

        <!-- 赛事列表 -->
        <div class="tournaments-grid"></div>

        <!-- 赛事详情 -->
        <div class="tournament-detail" style="display: none;"></div>
    </main>

    <!-- 底部导航由Navigation组件填充 -->
    <footer></footer>

    <!-- 组件 -->
    <script src="components/Navigation.js"></script>
    <script src="components/SearchBar.js"></script>
    <script src="components/Card.js"></script>

    <!-- 应用逻辑 -->
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 初始化导航
        Navigation.init();

        // 初始化搜索栏
        new SearchBar({
            placeholder: '搜索赛事...',
            onSearch: (query) => {
                searchTournaments(query);
            }
        });

        // 检查URL中是否有赛事ID
        const urlParams = new URLSearchParams(window.location.search);
        const tournamentId = urlParams.get('id');

        if (tournamentId) {
            // 显示赛事详情
            showTournamentDetail(tournamentId);
        } else {
            // 加载赛事列表
            loadTournaments();
        }

        // 设置过滤器点击事件
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                const category = btn.dataset.category;
                filterTournaments(category);
            });
        });

        // 搜索赛事
        function searchTournaments (query) {
            if (!query) {
                // 如果搜索词为空，显示所有赛事
                loadTournaments();
                return;
            }

            const tournaments = dataManager.getTournaments();
            const activeFilterBtn = document.querySelector('.filter-btn.active');
            const category = activeFilterBtn ? activeFilterBtn.dataset.category : 'all';

            // 先按类别筛选
            const filteredByCategory = category === 'all'
                ? tournaments
                : tournaments.filter(t => t.category === category);

            // 再按搜索词筛选
            const searchLower = query.toLowerCase();
            const filteredTournaments = filteredByCategory.filter(tournament =>
                tournament.name.toLowerCase().includes(searchLower) ||
                (tournament.description && tournament.description.toLowerCase().includes(searchLower)) ||
                (tournament.location && tournament.location.toLowerCase().includes(searchLower))
            );

            const tournamentsGrid = document.querySelector('.tournaments-grid');

            if (!filteredTournaments || filteredTournaments.length === 0) {
                tournamentsGrid.innerHTML = `<p class="no-data">没有找到符合"${query}"的赛事</p>`;
                return;
            }

            tournamentsGrid.innerHTML = filteredTournaments.map(tournament => `
                <div class="tournament-card">
                    <div class="card-header">
                        <h3>${tournament.name}</h3>
                    </div>
                    <div class="card-body">
                        <div class="tournament-info">
                            <p><strong>类别：</strong>${tournament.category || '未分类'}</p>
                            <p><strong>时间：</strong>${tournament.startDate || '未设置'} - ${tournament.endDate || '未设置'}</p>
                            <p><strong>地点：</strong>${tournament.location || '未指定'}</p>
                        </div>
                        <div class="tournament-stats">
                            <div class="tournament-stat">
                                <div class="stat-value">${tournament.teams && tournament.teams.length || 0}</div>
                                <div class="stat-label">参赛队伍</div>
                            </div>
                            <div class="tournament-stat">
                                <div class="stat-value">${tournament.matches && tournament.matches.length || 0}</div>
                                <div class="stat-label">比赛场数</div>
                            </div>
                            <div class="tournament-stat">
                                <div class="stat-value">${tournament.status === 'ongoing' ? '进行中' : tournament.status === 'completed' ? '已结束' : '未开始'}</div>
                                <div class="stat-label">状态</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-primary" onclick="showTournamentDetail('${tournament.id}')">查看详情</button>
                    </div>
                </div>
            `).join('');
        }
    </script>
</body>

</html>