/**
 * 搜索栏组件
 */
class SearchBar {
    /**
     * 生成搜索栏HTML
     * @param {string} id - 搜索输入框的ID
     * @param {string} placeholder - 搜索框占位文本
     * @returns {string} 搜索栏HTML
     */
    static render (id = 'search-input', placeholder = '搜索...') {
        return `
            <div class="search-bar">
                <div class="search-input-wrapper">
                    <input type="text" id="${id}" placeholder="${placeholder}">
                    <button id="search-btn" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 初始化搜索栏功能
     * @param {string} id - 搜索输入框的ID
     * @param {Function} searchCallback - 搜索回调函数
     */
    static init (id = 'search-input', searchCallback) {
        const searchInput = document.getElementById(id);
        const searchBtn = document.getElementById('search-btn');
        const searchToggle = document.getElementById('search-toggle');
        const searchBar = document.querySelector('.search-bar');

        if (searchToggle && searchBar) {
            searchToggle.addEventListener('click', function (e) {
                e.preventDefault();
                searchBar.classList.toggle('active');
                if (searchBar.classList.contains('active')) {
                    searchInput.focus();
                }
            });
        }

        if (searchInput && searchBtn && searchCallback) {
            // 点击搜索按钮时执行搜索
            searchBtn.addEventListener('click', () => {
                const searchTerm = searchInput.value.trim();
                searchCallback(searchTerm);
            });

            // 按下回车键时执行搜索
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const searchTerm = searchInput.value.trim();
                    searchCallback(searchTerm);
                }
            });
        }
    }
} 