/* 首页特定样式 */

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1rem;
    text-align: center;
    box-shadow: var(--card-shadow);
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0.5rem 0;
}

.stat-card .stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 最近赛事和比赛 */
.recent-section {
    margin: 2rem 0;
}

.recent-section h2 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.recent-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .recent-grid {
        grid-template-columns: 1fr;
    }
}