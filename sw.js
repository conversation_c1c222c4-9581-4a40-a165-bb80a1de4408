// Service Worker for 中国青少年足球数据平台
const CACHE_NAME = 'football-platform-v1.0.0';
const STATIC_CACHE = 'static-v1';
const DYNAMIC_CACHE = 'dynamic-v1';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/teams.html',
    '/tournaments.html',
    '/matches.html',
    '/players.html',
    '/manage.html',
    '/login.html',
    '/css/style.css',
    '/css/pages/teams.css',
    '/css/pages/tournaments.css',
    '/css/pages/matches.css',
    '/js/app.js',
    '/js/data.js',
    '/js/auth.js',
    '/js/init-data.js',
    '/components/Navigation.js',
    '/components/SearchBar.js',
    '/components/Card.js',
    '/images/logo.png',
    '/manifest.json'
];

// 安装事件 - 缓存静态资源
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Service Worker: Static assets cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Failed to cache static assets', error);
            })
    );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

// 拦截网络请求
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 只处理同源请求
    if (url.origin !== location.origin) {
        return;
    }
    
    // 对于HTML页面，使用网络优先策略
    if (request.headers.get('accept').includes('text/html')) {
        event.respondWith(
            fetch(request)
                .then(response => {
                    // 如果网络请求成功，更新缓存
                    const responseClone = response.clone();
                    caches.open(DYNAMIC_CACHE)
                        .then(cache => cache.put(request, responseClone));
                    return response;
                })
                .catch(() => {
                    // 网络失败时从缓存返回
                    return caches.match(request)
                        .then(response => {
                            return response || caches.match('/index.html');
                        });
                })
        );
        return;
    }
    
    // 对于其他资源，使用缓存优先策略
    event.respondWith(
        caches.match(request)
            .then(response => {
                if (response) {
                    return response;
                }
                
                // 如果缓存中没有，从网络获取
                return fetch(request)
                    .then(response => {
                        // 只缓存成功的响应
                        if (response.status === 200) {
                            const responseClone = response.clone();
                            caches.open(DYNAMIC_CACHE)
                                .then(cache => cache.put(request, responseClone));
                        }
                        return response;
                    })
                    .catch(error => {
                        console.log('Service Worker: Fetch failed', error);
                        // 返回离线页面或默认响应
                        if (request.destination === 'image') {
                            return caches.match('/images/logo.png');
                        }
                        throw error;
                    });
            })
    );
});

// 后台同步
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(
            // 这里可以添加后台同步逻辑
            console.log('Service Worker: Performing background sync')
        );
    }
});

// 推送通知
self.addEventListener('push', event => {
    console.log('Service Worker: Push received', event);
    
    const options = {
        body: event.data ? event.data.text() : '您有新的足球数据更新',
        icon: '/images/logo.png',
        badge: '/images/logo.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: '查看详情',
                icon: '/images/logo.png'
            },
            {
                action: 'close',
                title: '关闭',
                icon: '/images/logo.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('足球数据平台', options)
    );
});

// 通知点击事件
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification click', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// 错误处理
self.addEventListener('error', event => {
    console.error('Service Worker: Error', event.error);
});

// 未处理的Promise拒绝
self.addEventListener('unhandledrejection', event => {
    console.error('Service Worker: Unhandled promise rejection', event.reason);
});
