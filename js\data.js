/**
 * 中国青少年足球数据平台 - 数据管理模块
 * 
 * 该模块用于处理网站的数据存储和管理功能，包括：
 * - 初始化样例数据
 * - 提供数据存取API
 * - 处理数据的本地存储和读取
 */

// 存储键名
const STORAGE_KEYS = {
    TOURNAMENTS: 'tournaments',
    TEAMS: 'teams',
    PLAYERS: 'players',
    MATCHES: 'matches',
    USERS: 'users',
    CURRENT_USER: 'currentUser'
};

// 数据管理类
class FootballDataManager {
    constructor() {
        this.isAuthenticated = false;
        this.currentUser = null;
        this.userRoles = {
            viewer: 'viewer', // 只能查看数据
            editor: 'editor', // 可以修改数据
            admin: 'admin'    // 可以管理用户和导入/导出数据
        };

        // 检查用户登录状态
        this.loadCurrentUser();

        // 检查并初始化数据
        this.initData();

        // 检查用户权限
        this.checkUserPermission();

        // 监听认证状态变化
        document.addEventListener('authStateChanged', this.handleAuthStateChange.bind(this));
    }

    /**
     * 处理认证状态变化
     */
    handleAuthStateChange (event) {
        this.checkUserPermission();
    }

    /**
     * 加载当前用户信息
     */
    loadCurrentUser () {
        // 尝试从localStorage获取当前用户信息
        try {
            const userDataJson = localStorage.getItem(STORAGE_KEYS.CURRENT_USER);
            if (userDataJson) {
                this.currentUser = JSON.parse(userDataJson);
                this.isAuthenticated = true;
                console.log("已加载用户信息:", this.currentUser.username);
            } else {
                this.currentUser = null;
                this.isAuthenticated = false;
                console.log("未找到登录用户信息");
            }
        } catch (error) {
            console.error("加载用户信息失败:", error);
            this.currentUser = null;
            this.isAuthenticated = false;
        }
    }

    /**
     * 检查用户权限
     */
    checkUserPermission () {
        // 全局authManager在auth.js中定义
        if (window.authManager) {
            this.isAdmin = window.authManager.hasRole('admin');
            this.isEditor = window.authManager.hasRole('editor');
            this.isAuthenticated = window.authManager.isLoggedIn();
        } else {
            // auth.js尚未加载，先设为false
            this.isAdmin = false;
            this.isEditor = false;
            this.isAuthenticated = false;

            // 稍后再次检查
            setTimeout(() => this.checkUserPermission(), 500);
        }
    }

    /**
     * 初始化数据，如果本地存储中没有数据则创建示例数据
     */
    initData () {
        // 检查是否已存在数据
        if (!localStorage.getItem(STORAGE_KEYS.TOURNAMENTS)) {
            // 初始化示例数据
            this.initSampleData();
        }
    }

    /**
     * 初始化示例数据
     */
    initSampleData () {
        // 示例赛事数据
        const tournaments = [
            {
                id: 't1',
                name: '2023全国青少年足球联赛U15组',
                category: 'U15',
                startDate: '2023-05-01',
                endDate: '2023-09-30',
                location: '全国多地',
                description: '全国范围内U15年龄段的青少年足球联赛'
            },
            {
                id: 't2',
                name: '2023北京市青少年足球锦标赛U12组',
                category: 'U12',
                startDate: '2023-06-15',
                endDate: '2023-08-20',
                location: '北京市',
                description: '北京市范围内U12年龄段的青少年足球锦标赛'
            },
            {
                id: 't3',
                name: '2023广州市青少年足球冠军杯U17组',
                category: 'U17',
                startDate: '2023-07-10',
                endDate: '2023-10-15',
                location: '广州市',
                description: '广州市范围内U17年龄段的青少年足球冠军杯赛事'
            }
        ];

        // 示例球队数据
        const teams = [
            {
                id: 'team1',
                name: '北京青年足球俱乐部',
                region: '北京',
                founded: '2010-01-15',
                coach: '李明',
                logo: 'images/teams/beijing_youth.png',
                description: '北京市知名青训俱乐部，多次在全国青少年联赛中取得好成绩'
            },
            {
                id: 'team2',
                name: '上海少年足球学院',
                region: '上海',
                founded: '2008-03-20',
                coach: '张伟',
                logo: 'images/teams/shanghai_youth.png',
                description: '上海市专业青训机构，培养了多名国家队球员'
            },
            {
                id: 'team3',
                name: '广州飞鹰足球俱乐部',
                region: '广州',
                founded: '2012-05-10',
                coach: '王强',
                logo: 'images/teams/guangzhou_eagles.png',
                description: '广州地区知名青训俱乐部，以技术足球著称'
            },
            {
                id: 'team4',
                name: '成都未来之星足球学校',
                region: '成都',
                founded: '2015-09-01',
                coach: '陈勇',
                logo: 'images/teams/chengdu_stars.png',
                description: '成都本地青训机构，注重基础培养和球员全面发展'
            }
        ];

        // 示例球员数据
        const players = [
            {
                id: 'p1',
                name: '刘杰',
                teamId: 'team1',
                position: 'FW',
                birthDate: '2008-03-15',
                height: 175,
                weight: 65,
                description: '速度快，射门准，是球队的主力射手'
            },
            {
                id: 'p2',
                name: '王浩',
                teamId: 'team1',
                position: 'MF',
                birthDate: '2008-07-22',
                height: 170,
                weight: 62,
                description: '技术全面，视野开阔，是球队的中场核心'
            },
            {
                id: 'p3',
                name: '张锋',
                teamId: 'team2',
                position: 'DF',
                birthDate: '2008-05-10',
                height: 180,
                weight: 70,
                description: '防守稳健，头球能力强，是球队后防线的支柱'
            },
            {
                id: 'p4',
                name: '李明',
                teamId: 'team2',
                position: 'GK',
                birthDate: '2009-02-18',
                height: 182,
                weight: 75,
                description: '反应敏捷，出击果断，是一名有潜力的门将'
            },
            {
                id: 'p5',
                name: '赵强',
                teamId: 'team3',
                position: 'FW',
                birthDate: '2007-11-05',
                height: 178,
                weight: 68,
                description: '技术精湛，盘带能力强，能够创造得分机会'
            },
            {
                id: 'p6',
                name: '陈勇',
                teamId: 'team3',
                position: 'MF',
                birthDate: '2007-09-12',
                height: 172,
                weight: 63,
                description: '传球准确，战术意识强，是球队的组织者'
            },
            {
                id: 'p7',
                name: '黄威',
                teamId: 'team4',
                position: 'DF',
                birthDate: '2008-08-25',
                height: 176,
                weight: 67,
                description: '速度快，位置感好，能够有效覆盖防守区域'
            },
            {
                id: 'p8',
                name: '吴刚',
                teamId: 'team4',
                position: 'MF',
                birthDate: '2008-12-30',
                height: 168,
                weight: 60,
                description: '灵活敏捷，控球能力强，能够突破防线'
            }
        ];

        // 示例比赛数据
        const matches = [
            {
                id: 'm1',
                tournamentId: 't1',
                homeTeamId: 'team1',
                awayTeamId: 'team2',
                date: '2023-05-15',
                time: '15:30',
                venue: '北京青少年足球中心',
                homeScore: 2,
                awayScore: 1,
                status: 'completed',
                highlights: '精彩的比赛，主队在最后时刻打进制胜球'
            },
            {
                id: 'm2',
                tournamentId: 't1',
                homeTeamId: 'team3',
                awayTeamId: 'team4',
                date: '2023-05-16',
                time: '14:00',
                venue: '广州市体育中心',
                homeScore: 0,
                awayScore: 0,
                status: 'completed',
                highlights: '双方防守出色，最终战成平局'
            },
            {
                id: 'm3',
                tournamentId: 't2',
                homeTeamId: 'team1',
                awayTeamId: 'team3',
                date: '2023-06-20',
                time: '16:00',
                venue: '北京市奥体中心',
                homeScore: 3,
                awayScore: 2,
                status: 'completed',
                highlights: '进球不断，双方攻势如潮'
            },
            {
                id: 'm4',
                tournamentId: 't2',
                homeTeamId: 'team2',
                awayTeamId: 'team4',
                date: '2023-06-22',
                time: '15:00',
                venue: '上海体育场',
                homeScore: 1,
                awayScore: 2,
                status: 'completed',
                highlights: '客队顽强拼搏，最终逆转取胜'
            },
            {
                id: 'm5',
                tournamentId: 't3',
                homeTeamId: 'team1',
                awayTeamId: 'team4',
                date: '2023-07-25',
                time: '16:30',
                venue: '北京工人体育场',
                homeScore: null,
                awayScore: null,
                status: 'scheduled',
                highlights: ''
            },
            {
                id: 'm6',
                tournamentId: 't3',
                homeTeamId: 'team2',
                awayTeamId: 'team3',
                date: '2023-07-28',
                time: '15:30',
                venue: '上海虹口足球场',
                homeScore: null,
                awayScore: null,
                status: 'scheduled',
                highlights: ''
            }
        ];

        // 将数据保存到本地存储
        this.saveTournaments(tournaments);
        this.saveTeams(teams);
        this.savePlayers(players);
        this.saveMatches(matches);
    }

    // 赛事数据方法
    getTournaments () {
        // 确保即使未登录也能获取赛事数据
        console.log("获取赛事数据，认证状态:", this.isAuthenticated);
        try {
            return JSON.parse(localStorage.getItem(STORAGE_KEYS.TOURNAMENTS) || '[]');
        } catch (error) {
            console.error("获取赛事数据失败:", error);
            return [];
        }
    }

    getTournamentById (id) {
        const tournaments = this.getTournaments();
        return tournaments.find(tournament => tournament.id === id) || null;
    }

    saveTournaments (tournaments) {
        localStorage.setItem(STORAGE_KEYS.TOURNAMENTS, JSON.stringify(tournaments));
    }

    /**
     * 添加赛事
     * 需要编辑权限
     */
    addTournament (tournament) {
        // 检查权限
        if (!this.isEditor && !this.isAdmin) {
            console.error('权限不足：需要编辑者或管理员权限');
            return { success: false, message: '权限不足' };
        }

        // 获取现有赛事
        const tournaments = this.getTournaments();

        // 设置新赛事ID
        if (!tournament.id) {
            tournament.id = 't' + (tournaments.length + 1);
        }

        // 添加创建者和修改时间信息
        tournament.createdBy = window.authManager ? window.authManager.getCurrentUser()?.username : 'unknown';
        tournament.createdAt = new Date().toISOString();
        tournament.updatedAt = new Date().toISOString();

        // 添加到数组
        tournaments.push(tournament);

        // 保存到存储
        this.saveTournaments(tournaments);

        return { success: true, id: tournament.id };
    }

    /**
     * 更新赛事
     * 需要编辑权限
     */
    updateTournament (tournament) {
        // 检查权限
        if (!this.isEditor && !this.isAdmin) {
            console.error('权限不足：需要编辑者或管理员权限');
            return { success: false, message: '权限不足' };
        }

        // 获取现有赛事
        const tournaments = this.getTournaments();

        // 查找要更新的赛事索引
        const index = tournaments.findIndex(t => t.id === tournament.id);

        if (index === -1) {
            console.error('找不到要更新的赛事:', tournament.id);
            return { success: false, message: '找不到赛事' };
        }

        // 更新修改信息
        tournament.updatedBy = window.authManager ? window.authManager.getCurrentUser()?.username : 'unknown';
        tournament.updatedAt = new Date().toISOString();

        // 保留创建信息
        tournament.createdBy = tournaments[index].createdBy;
        tournament.createdAt = tournaments[index].createdAt;

        // 更新赛事
        tournaments[index] = tournament;

        // 保存到存储
        this.saveTournaments(tournaments);

        return { success: true };
    }

    /**
     * 删除赛事
     * 需要管理员权限
     */
    deleteTournament (id) {
        // 检查权限
        if (!this.isAdmin) {
            console.error('权限不足：需要管理员权限');
            return { success: false, message: '权限不足' };
        }

        // 获取现有赛事
        const tournaments = this.getTournaments();

        // 筛选出要保留的赛事
        const filteredTournaments = tournaments.filter(t => t.id !== id);

        // 如果数组长度没变，说明没找到要删除的赛事
        if (tournaments.length === filteredTournaments.length) {
            console.error('找不到要删除的赛事:', id);
            return { success: false, message: '找不到赛事' };
        }

        // 保存到存储
        this.saveTournaments(filteredTournaments);

        return { success: true };
    }

    // 球队数据方法
    getTeams () {
        return JSON.parse(localStorage.getItem(STORAGE_KEYS.TEAMS) || '[]');
    }

    getTeamById (id) {
        const teams = this.getTeams();
        return teams.find(team => team.id === id) || null;
    }

    saveTeams (teams) {
        localStorage.setItem(STORAGE_KEYS.TEAMS, JSON.stringify(teams));
    }

    addTeam (team) {
        const teams = this.getTeams();
        // 生成唯一ID
        team.id = 'team' + Date.now();
        teams.push(team);
        this.saveTeams(teams);
        return team;
    }

    updateTeam (team) {
        const teams = this.getTeams();
        const index = teams.findIndex(t => t.id === team.id);
        if (index !== -1) {
            teams[index] = team;
            this.saveTeams(teams);
            return true;
        }
        return false;
    }

    deleteTeam (id) {
        const teams = this.getTeams();
        const newTeams = teams.filter(team => team.id !== id);
        if (newTeams.length < teams.length) {
            this.saveTeams(newTeams);
            return true;
        }
        return false;
    }

    // 球员数据方法
    getPlayers () {
        return JSON.parse(localStorage.getItem(STORAGE_KEYS.PLAYERS) || '[]');
    }

    getPlayerById (id) {
        const players = this.getPlayers();
        return players.find(player => player.id === id) || null;
    }

    getPlayersByTeamId (teamId) {
        const players = this.getPlayers();
        return players.filter(player => player.teamId === teamId);
    }

    savePlayers (players) {
        localStorage.setItem(STORAGE_KEYS.PLAYERS, JSON.stringify(players));
    }

    addPlayer (player) {
        const players = this.getPlayers();
        // 生成唯一ID
        player.id = 'p' + Date.now();
        players.push(player);
        this.savePlayers(players);
        return player;
    }

    updatePlayer (player) {
        const players = this.getPlayers();
        const index = players.findIndex(p => p.id === player.id);
        if (index !== -1) {
            players[index] = player;
            this.savePlayers(players);
            return true;
        }
        return false;
    }

    deletePlayer (id) {
        const players = this.getPlayers();
        const newPlayers = players.filter(player => player.id !== id);
        if (newPlayers.length < players.length) {
            this.savePlayers(newPlayers);
            return true;
        }
        return false;
    }

    // 比赛数据方法
    getMatches () {
        return JSON.parse(localStorage.getItem(STORAGE_KEYS.MATCHES) || '[]');
    }

    getMatchById (id) {
        console.log("查找比赛，ID:", id);
        const matches = this.getMatches();
        const match = matches.find(match => match.id === id);
        console.log("找到比赛:", match);
        return match;
    }

    getMatchesByTournamentId (tournamentId) {
        const matches = this.getMatches();
        return matches.filter(match => match.tournamentId === tournamentId);
    }

    getMatchesByTeamId (teamId) {
        const matches = this.getMatches();
        return matches.filter(match => match.homeTeamId === teamId || match.awayTeamId === teamId);
    }

    saveMatches (matches) {
        localStorage.setItem(STORAGE_KEYS.MATCHES, JSON.stringify(matches));
    }

    addMatch (match) {
        const matches = this.getMatches();
        // 生成唯一ID
        match.id = 'm' + Date.now();
        matches.push(match);
        this.saveMatches(matches);
        return match;
    }

    updateMatch (match) {
        const matches = this.getMatches();
        const index = matches.findIndex(m => m.id === match.id);
        if (index !== -1) {
            matches[index] = match;
            this.saveMatches(matches);
            return true;
        }
        return false;
    }

    deleteMatch (id) {
        const matches = this.getMatches();
        const newMatches = matches.filter(match => match.id !== id);
        if (newMatches.length < matches.length) {
            this.saveMatches(newMatches);
            return true;
        }
        return false;
    }

    /**
     * 导出所有数据
     * 任何登录用户都可以导出
     */
    exportAllData () {
        // 检查是否已登录
        if (!this.isAuthenticated) {
            console.error('未登录：需要登录才能导出数据');
            return { success: false, message: '请先登录' };
        }

        // 收集所有数据
        const allData = {
            tournaments: this.getTournaments(),
            teams: this.getTeams(),
            players: this.getPlayers(),
            matches: this.getMatches(),
            exportTime: new Date().toISOString(),
            exportedBy: window.authManager ? window.authManager.getCurrentUser()?.username : 'unknown'
        };

        // 更新最后备份时间
        localStorage.setItem(STORAGE_KEYS.LAST_BACKUP, new Date().toISOString());

        return {
            success: true,
            data: allData,
            filename: `football_data_${new Date().toISOString().split('T')[0]}.json`
        };
    }

    /**
     * 导入所有数据
     * 需要管理员权限
     */
    importAllData (data) {
        // 检查权限
        if (!this.isAdmin) {
            console.error('权限不足：需要管理员权限');
            return { success: false, message: '权限不足' };
        }

        try {
            // 验证数据格式
            if (!data.tournaments || !data.teams || !data.players || !data.matches) {
                return { success: false, message: '数据格式无效' };
            }

            // 导入各类数据
            this.saveTournaments(data.tournaments);
            this.saveTeams(data.teams);
            this.savePlayers(data.players);
            this.saveMatches(data.matches);

            // 记录导入信息
            const importInfo = {
                importTime: new Date().toISOString(),
                importedBy: window.authManager ? window.authManager.getCurrentUser()?.username : 'unknown',
                originalExportTime: data.exportTime,
                originalExportedBy: data.exportedBy
            };

            localStorage.setItem('football_last_import', JSON.stringify(importInfo));

            return { success: true };
        } catch (error) {
            console.error('导入数据失败:', error);
            return { success: false, message: '导入失败: ' + error.message };
        }
    }

    /**
     * 清空所有数据
     * 需要管理员权限
     */
    clearAllData () {
        // 检查权限
        if (!this.isAdmin) {
            console.error('权限不足：需要管理员权限');
            return { success: false, message: '权限不足' };
        }

        // 清空所有数据
        localStorage.removeItem(STORAGE_KEYS.TOURNAMENTS);
        localStorage.removeItem(STORAGE_KEYS.TEAMS);
        localStorage.removeItem(STORAGE_KEYS.PLAYERS);
        localStorage.removeItem(STORAGE_KEYS.MATCHES);
        localStorage.removeItem(STORAGE_KEYS.LAST_BACKUP);

        // 初始化示例数据
        this.initSampleData();

        return { success: true };
    }

    // 获取数据统计
    getDataStats () {
        return {
            tournamentCount: this.getTournaments().length,
            teamCount: this.getTeams().length,
            playerCount: this.getPlayers().length,
            matchCount: this.getMatches().length,
            lastBackup: localStorage.getItem(STORAGE_KEYS.LAST_BACKUP) || null,
            dataSize: this.getDataSizeInKB()
        };
    }

    // 计算数据大小（KB）
    getDataSizeInKB () {
        const dataSize = (
            (localStorage.getItem(STORAGE_KEYS.TOURNAMENTS) || '').length +
            (localStorage.getItem(STORAGE_KEYS.TEAMS) || '').length +
            (localStorage.getItem(STORAGE_KEYS.PLAYERS) || '').length +
            (localStorage.getItem(STORAGE_KEYS.MATCHES) || '').length
        ) / 1024;

        return Math.round(dataSize * 100) / 100; // 保留两位小数
    }
}

// 注意：数据管理器实例已在app.js中创建 