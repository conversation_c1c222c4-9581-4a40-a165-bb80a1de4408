<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国青少年足球数据平台</title>
    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css">
    <!-- 页面特定样式 -->
    <link rel="stylesheet" href="css/pages/home.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- 导航栏由Navigation组件填充 -->
    <header></header>

    <main>
        <div class="page-header">
            <h2><i class="fas fa-home"></i> 首页</h2>
        </div>

        <!-- 搜索栏 -->
        <div id="search-container"></div>

        <!-- 统计数据 -->
        <section class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="tournament-count">0</div>
                <div class="stat-label">赛事总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="team-count">0</div>
                <div class="stat-label">球队总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="player-count">0</div>
                <div class="stat-label">球员总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="match-count">0</div>
                <div class="stat-label">比赛总数</div>
            </div>
        </section>

        <!-- 最近赛事 -->
        <section class="recent-section">
            <h2><i class="fas fa-trophy"></i> 最近赛事</h2>
            <div id="recent-tournaments" class="recent-grid"></div>
        </section>

        <!-- 最近比赛 -->
        <section class="recent-section">
            <h2><i class="fas fa-futbol"></i> 最近比赛</h2>
            <div id="recent-matches" class="recent-grid"></div>
        </section>
    </main>

    <!-- 底部导航由Navigation组件填充 -->
    <footer></footer>

    <!-- 组件 -->
    <script src="components/Navigation.js"></script>
    <script src="components/SearchBar.js"></script>
    <script src="components/Card.js"></script>
    <!-- 数据和应用逻辑 -->
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>
    <script>
        // 初始化导航
        Navigation.init();

        document.addEventListener('DOMContentLoaded', function () {
            // 初始化搜索栏
            document.getElementById('search-container').innerHTML = SearchBar.render();
            SearchBar.init();
        });
    </script>
</body>

</html>