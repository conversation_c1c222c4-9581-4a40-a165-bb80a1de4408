<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国青少年足球数据平台</title>
    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css">
    <!-- 页面特定样式 -->
    <link rel="stylesheet" href="css/pages/home.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- 导航栏由Navigation组件填充 -->
    <header></header>

    <main>
        <div class="page-header">
            <h2><i class="fas fa-home"></i> 首页</h2>
        </div>

        <!-- 搜索栏 -->
        <div id="search-container"></div>

        <!-- HTML5轮播图 -->
        <section class="hero-carousel" id="hero-carousel">
            <div class="carousel-container">
                <div class="carousel-slides">
                    <div class="carousel-slide active" data-slide="0">
                        <div class="slide-content">
                            <div class="slide-image">
                                <img src="https://via.placeholder.com/400x200/2ecc71/ffffff?text=青少年足球训练" alt="青少年足球训练"
                                    loading="lazy"
                                    onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMmVjYzcxIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPumdkuWwkeW5tOi2s+eQg+iuree7gyDwn5GmPC90ZXh0Pjwvc3ZnPg=='">
                            </div>
                            <div class="slide-text">
                                <h3>专业青少年足球训练</h3>
                                <p>培养下一代足球明星，从基础技能到战术理解</p>
                                <button class="slide-btn" onclick="window.location.href='teams.html'">
                                    <i class="fas fa-users"></i> 查看球队
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="carousel-slide" data-slide="1">
                        <div class="slide-content">
                            <div class="slide-image">
                                <img src="https://via.placeholder.com/400x200/27ae60/ffffff?text=精彩比赛瞬间" alt="精彩比赛瞬间"
                                    loading="lazy"
                                    onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMjdhZTYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuexvuW9qeavlOi1m+eehOmXtCDimb08L3RleHQ+PC9zdmc+'">
                            </div>
                            <div class="slide-text">
                                <h3>精彩比赛瞬间</h3>
                                <p>记录每一个精彩瞬间，见证青少年足球的成长</p>
                                <button class="slide-btn" onclick="window.location.href='matches.html'">
                                    <i class="fas fa-futbol"></i> 观看比赛
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="carousel-slide" data-slide="2">
                        <div class="slide-content">
                            <div class="slide-image">
                                <img src="https://via.placeholder.com/400x200/3498db/ffffff?text=赛事管理系统" alt="赛事管理系统"
                                    loading="lazy"
                                    onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzQ5OGRiIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPui1m+S6i+euoeeQhuezuzwvdGV4dD48L3N2Zz4='">
                            </div>
                            <div class="slide-text">
                                <h3>现代化赛事管理</h3>
                                <p>使用HTML5技术打造的智能赛事管理平台</p>
                                <button class="slide-btn" onclick="window.location.href='tournaments.html'">
                                    <i class="fas fa-trophy"></i> 查看赛事
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 轮播控制按钮 -->
                <button class="carousel-btn carousel-prev" id="carousel-prev" aria-label="上一张">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="carousel-btn carousel-next" id="carousel-next" aria-label="下一张">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <!-- 轮播指示器 -->
                <div class="carousel-indicators">
                    <button class="indicator active" data-slide="0" aria-label="第1张"></button>
                    <button class="indicator" data-slide="1" aria-label="第2张"></button>
                    <button class="indicator" data-slide="2" aria-label="第3张"></button>
                </div>
            </div>
        </section>

        <!-- 统计数据 -->
        <section class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="tournament-count">0</div>
                <div class="stat-label">赛事总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="team-count">0</div>
                <div class="stat-label">球队总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="player-count">0</div>
                <div class="stat-label">球员总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="match-count">0</div>
                <div class="stat-label">比赛总数</div>
            </div>
        </section>

        <!-- 最近赛事 -->
        <section class="recent-section">
            <h2><i class="fas fa-trophy"></i> 最近赛事</h2>
            <div id="recent-tournaments" class="recent-grid"></div>
        </section>

        <!-- 最近比赛 -->
        <section class="recent-section">
            <h2><i class="fas fa-futbol"></i> 最近比赛</h2>
            <div id="recent-matches" class="recent-grid"></div>
        </section>
    </main>

    <!-- 底部导航由Navigation组件填充 -->
    <footer></footer>

    <!-- 组件 -->
    <script src="components/Navigation.js"></script>
    <script src="components/SearchBar.js"></script>
    <script src="components/Card.js"></script>
    <!-- 数据和应用逻辑 -->
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>
    <script>
        // 初始化导航
        Navigation.init();

        document.addEventListener('DOMContentLoaded', function () {
            // 初始化搜索栏
            document.getElementById('search-container').innerHTML = SearchBar.render();
            SearchBar.init();

            // 初始化轮播图
            initCarousel();
        });

        // HTML5轮播图功能
        function initCarousel () {
            console.log('🎠 初始化轮播图...');

            const carousel = document.getElementById('hero-carousel');
            const slides = carousel.querySelectorAll('.carousel-slide');
            const indicators = carousel.querySelectorAll('.indicator');
            const prevBtn = document.getElementById('carousel-prev');
            const nextBtn = document.getElementById('carousel-next');

            let currentSlide = 0;
            let autoPlayInterval;
            const autoPlayDelay = 5000; // 5秒自动切换

            // 显示指定幻灯片
            function showSlide (index) {
                // 移除所有活跃状态
                slides.forEach(slide => slide.classList.remove('active'));
                indicators.forEach(indicator => indicator.classList.remove('active'));

                // 设置当前幻灯片为活跃状态
                slides[index].classList.add('active');
                indicators[index].classList.add('active');

                currentSlide = index;
            }

            // 下一张
            function nextSlide () {
                const next = (currentSlide + 1) % slides.length;
                showSlide(next);
            }

            // 上一张
            function prevSlide () {
                const prev = (currentSlide - 1 + slides.length) % slides.length;
                showSlide(prev);
            }

            // 开始自动播放
            function startAutoPlay () {
                autoPlayInterval = setInterval(nextSlide, autoPlayDelay);
            }

            // 停止自动播放
            function stopAutoPlay () {
                if (autoPlayInterval) {
                    clearInterval(autoPlayInterval);
                }
            }

            // 绑定事件
            nextBtn.addEventListener('click', () => {
                nextSlide();
                stopAutoPlay();
                setTimeout(startAutoPlay, 3000); // 3秒后重新开始自动播放
            });

            prevBtn.addEventListener('click', () => {
                prevSlide();
                stopAutoPlay();
                setTimeout(startAutoPlay, 3000);
            });

            // 指示器点击事件
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    showSlide(index);
                    stopAutoPlay();
                    setTimeout(startAutoPlay, 3000);
                });
            });

            // 鼠标悬停时暂停自动播放
            carousel.addEventListener('mouseenter', stopAutoPlay);
            carousel.addEventListener('mouseleave', startAutoPlay);

            // 触摸滑动支持（移动端）
            let touchStartX = 0;
            let touchEndX = 0;

            carousel.addEventListener('touchstart', (e) => {
                touchStartX = e.changedTouches[0].screenX;
            });

            carousel.addEventListener('touchend', (e) => {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });

            function handleSwipe () {
                const swipeThreshold = 50;
                const diff = touchStartX - touchEndX;

                if (Math.abs(diff) > swipeThreshold) {
                    if (diff > 0) {
                        nextSlide(); // 向左滑动，显示下一张
                    } else {
                        prevSlide(); // 向右滑动，显示上一张
                    }
                    stopAutoPlay();
                    setTimeout(startAutoPlay, 3000);
                }
            }

            // 键盘导航支持
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') {
                    prevSlide();
                    stopAutoPlay();
                    setTimeout(startAutoPlay, 3000);
                } else if (e.key === 'ArrowRight') {
                    nextSlide();
                    stopAutoPlay();
                    setTimeout(startAutoPlay, 3000);
                }
            });

            // 开始自动播放
            startAutoPlay();

            console.log('轮播图初始化完成');
        }
    </script>
</body>

</html>