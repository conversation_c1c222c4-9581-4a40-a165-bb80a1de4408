/**
 * 导航组件
 */
class Navigation {
    /**
     * 生成顶部导航栏
     * @returns {string} 顶部导航栏HTML
     */
    static renderTopNav () {
        return `
            <nav class="top-nav">
                <div class="nav-logo">
                    <a href="index.html">
                        <img src="images/logo.png" alt="青少年足球">
                        <span>青少年足球</span>
                    </a>
                </div>
                <button id="search-toggle" class="search-toggle">
                    <i class="fas fa-search"></i>
                </button>
                <div class="nav-links">
                    <a href="index.html">首页</a>
                    <a href="tournaments.html">赛事</a>
                    <a href="teams.html">球队</a>
                    <a href="players.html">球员</a>
                    <a href="matches.html">比赛</a>
                    <a href="manage.html">管理</a>
                </div>
            </nav>
        `;
    }

    /**
     * 生成底部导航栏
     * @returns {string} 底部导航栏HTML
     */
    static renderBottomNav () {
        return `
            <nav class="bottom-nav">
                <a href="index.html">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <a href="tournaments.html">
                    <i class="fas fa-trophy"></i>
                    <span>赛事</span>
                </a>
                <a href="teams.html">
                    <i class="fas fa-users"></i>
                    <span>球队</span>
                </a>
                <a href="players.html">
                    <i class="fas fa-user"></i>
                    <span>球员</span>
                </a>
                <a href="matches.html">
                    <i class="fas fa-futbol"></i>
                    <span>比赛</span>
                </a>
            </nav>
        `;
    }

    /**
     * 初始化导航组件
     */
    static init () {
        // 渲染导航栏
        document.querySelector('header').innerHTML = this.renderTopNav();
        document.querySelector('footer').innerHTML = this.renderBottomNav();

        // 初始化导航高亮
        this.initActivePage();
    }

    /**
     * 初始化当前页面导航高亮
     */
    static initActivePage () {
        const currentPath = window.location.pathname;
        const pageName = currentPath.split('/').pop();

        // 高亮底部导航相应菜单项
        const bottomNavLinks = document.querySelectorAll('.bottom-nav a');
        bottomNavLinks.forEach(link => {
            if (link.getAttribute('href') === pageName) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });

        // 高亮顶部导航相应菜单项
        const topNavLinks = document.querySelectorAll('.top-nav .nav-links a');
        topNavLinks.forEach(link => {
            if (link.getAttribute('href') === pageName) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }
} 