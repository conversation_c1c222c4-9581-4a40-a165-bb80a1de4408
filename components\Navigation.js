/**
 * 统一导航组件 - HTML5语义化设计
 */
class Navigation {
    /**
     * 生成统一的顶部导航栏
     * @returns {string} 顶部导航栏HTML
     */
    static renderTopNav () {
        return `
            <nav class="top-nav" role="navigation" aria-label="主导航">
                <div class="nav-brand">
                    <a href="index.html" class="brand-link" aria-label="返回首页">
                        <img src="images/logo.png" alt="中国青少年足球数据平台" class="brand-logo">
                        <div class="brand-text">
                            <span class="brand-title">青少年足球</span>
                            <small class="brand-subtitle">数据平台</small>
                        </div>
                    </a>
                </div>

                <div class="nav-actions">
                    <button id="search-toggle" class="nav-action-btn" title="搜索" aria-label="打开搜索">
                        <i class="fas fa-search"></i>
                    </button>
                    <button id="user-menu-toggle" class="nav-action-btn" title="用户菜单" aria-label="用户菜单">
                        <i class="fas fa-user-circle"></i>
                    </button>
                </div>

                <!-- 用户菜单下拉 -->
                <div id="user-menu" class="user-menu" style="display: none;">
                    <div class="user-info">
                        <i class="fas fa-user-circle"></i>
                        <span id="current-user">游客</span>
                    </div>
                    <div class="menu-divider"></div>
                    <a href="login.html" class="menu-item" id="login-logout-btn">
                        <i class="fas fa-sign-in-alt"></i> 登录
                    </a>
                </div>
            </nav>
        `;
    }

    /**
     * 生成统一的底部导航栏
     * @returns {string} 底部导航栏HTML
     */
    static renderBottomNav () {
        return `
            <nav class="bottom-nav" role="navigation" aria-label="主要功能导航">
                <a href="index.html" class="nav-item" data-page="index" aria-label="首页">
                    <div class="nav-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <span class="nav-label">首页</span>
                </a>
                <a href="tournaments.html" class="nav-item" data-page="tournaments" aria-label="赛事管理">
                    <div class="nav-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <span class="nav-label">赛事</span>
                </a>
                <a href="teams.html" class="nav-item" data-page="teams" aria-label="球队管理">
                    <div class="nav-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <span class="nav-label">球队</span>
                </a>
                <a href="players.html" class="nav-item" data-page="players" aria-label="球员管理">
                    <div class="nav-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <span class="nav-label">球员</span>
                </a>
                <a href="matches.html" class="nav-item" data-page="matches" aria-label="比赛管理">
                    <div class="nav-icon">
                        <i class="fas fa-futbol"></i>
                    </div>
                    <span class="nav-label">比赛</span>
                </a>
            </nav>
        `;
    }

    /**
     * 初始化统一导航组件
     */
    static init () {
        console.log('🧭 初始化统一导航组件...');

        // 渲染导航栏
        const header = document.querySelector('header');
        const footer = document.querySelector('footer');

        if (header) {
            header.innerHTML = this.renderTopNav();
        }

        if (footer) {
            footer.innerHTML = this.renderBottomNav();
        }

        // 初始化导航功能
        this.initActivePage();
        this.initUserMenu();
        this.initSearchToggle();
    }

    /**
     * 初始化当前页面导航高亮
     */
    static initActivePage () {
        const currentPath = window.location.pathname;
        const pageName = currentPath.split('/').pop() || 'index.html';

        // 高亮底部导航相应菜单项
        const bottomNavItems = document.querySelectorAll('.bottom-nav .nav-item');
        bottomNavItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href === pageName || (pageName === '' && href === 'index.html')) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    /**
     * 初始化用户菜单
     */
    static initUserMenu () {
        const userMenuToggle = document.getElementById('user-menu-toggle');
        const userMenu = document.getElementById('user-menu');

        if (userMenuToggle && userMenu) {
            userMenuToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                const isVisible = userMenu.style.display !== 'none';
                userMenu.style.display = isVisible ? 'none' : 'block';
            });

            // 点击其他地方关闭菜单
            document.addEventListener('click', () => {
                userMenu.style.display = 'none';
            });

            // 阻止菜单内部点击事件冒泡
            userMenu.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // 更新用户信息
        this.updateUserInfo();
    }

    /**
     * 初始化搜索切换
     */
    static initSearchToggle () {
        const searchToggle = document.getElementById('search-toggle');
        if (searchToggle) {
            searchToggle.addEventListener('click', () => {
                // 触发搜索功能
                const searchContainer = document.getElementById('search-container');
                if (searchContainer) {
                    const searchInput = searchContainer.querySelector('input');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
            });
        }
    }

    /**
     * 更新用户信息显示
     */
    static updateUserInfo () {
        const currentUserSpan = document.getElementById('current-user');
        const loginLogoutBtn = document.getElementById('login-logout-btn');

        // 检查用户登录状态（这里使用localStorage模拟）
        const currentUser = localStorage.getItem('currentUser');

        if (currentUser && currentUserSpan && loginLogoutBtn) {
            const userData = JSON.parse(currentUser);
            currentUserSpan.textContent = userData.username || '用户';
            loginLogoutBtn.innerHTML = '<i class="fas fa-sign-out-alt"></i> 退出登录';
            loginLogoutBtn.href = '#';
            loginLogoutBtn.onclick = () => {
                localStorage.removeItem('currentUser');
                window.location.reload();
            };
        } else if (currentUserSpan && loginLogoutBtn) {
            currentUserSpan.textContent = '游客';
            loginLogoutBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录';
            loginLogoutBtn.href = 'login.html';
            loginLogoutBtn.onclick = null;
        }
    }
}