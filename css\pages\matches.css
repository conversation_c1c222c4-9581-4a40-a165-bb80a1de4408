/* 比赛页面特定样式 */

/* 比赛卡片网格 */
.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

/* 比赛卡片 */
.match-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease;
}

.match-card:hover {
    transform: translateY(-5px);
}

.match-card .card-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.match-card .card-header.completed {
    background: var(--success-color);
    color: white;
}

.match-card .card-header.live {
    background: var(--warning-color);
    color: white;
}

.match-card .card-header.upcoming {
    background: var(--info-color);
    color: white;
}

.match-card .card-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.match-date {
    font-size: 0.9rem;
    opacity: 0.9;
}

.match-card .card-body {
    padding: 1.5rem 1rem;
}

.match-teams {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
}

.team {
    text-align: center;
    font-weight: bold;
    color: var(--text-primary);
}

.match-score {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-primary);
}

.match-time {
    font-size: 1.2rem;
    color: var(--text-secondary);
}

.match-venue {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* 比赛详情 */
.match-detail {
    padding: 2rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.match-detail-header {
    text-align: center;
    margin-bottom: 2rem;
}

.match-detail-teams {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
    margin: 2rem 0;
}

.match-detail-team {
    text-align: center;
}

.match-detail-logo {
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem;
}

.match-detail-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.match-score-large {
    font-size: 3rem;
    font-weight: bold;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.match-time-large {
    font-size: 2rem;
    color: var(--text-secondary);
}

.match-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.match-info-item {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--border-radius);
}

.match-info-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.match-info-value {
    color: var(--text-primary);
    font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .matches-grid {
        grid-template-columns: 1fr;
    }

    .match-detail {
        padding: 1rem;
    }

    .match-detail-teams {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .match-score-large {
        margin: 1rem 0;
        justify-content: center;
    }
}