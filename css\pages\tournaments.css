/* 赛事页面特定样式 */

/* 赛事卡片网格 */
.tournaments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

/* 赛事卡片 */
.tournament-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease;
}

.tournament-card:hover {
    transform: translateY(-5px);
}

.tournament-card .card-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.tournament-card .card-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.tournament-card .card-body {
    padding: 1rem;
}

.tournament-info {
    margin-bottom: 1rem;
}

.tournament-info p {
    margin: 0.5rem 0;
    color: var(--text-secondary);
}

.tournament-info strong {
    color: var(--text-primary);
}

.tournament-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-top: 1rem;
}

.tournament-stat {
    text-align: center;
    padding: 0.5rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
}

.tournament-stat .stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.tournament-stat .stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.tournament-card .card-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

/* 赛事详情 */
.tournament-detail {
    padding: 2rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.tournament-detail-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
}

.tournament-detail-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.tournament-detail-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.tournament-detail-section {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
}

.tournament-detail-section h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.tournament-matches {
    margin-top: 2rem;
}

.tournament-matches h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.tournament-teams {
    margin-top: 2rem;
}

.tournament-teams h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.team-mini-card {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.team-mini-logo {
    width: 60px;
    height: 60px;
    margin: 0 auto 0.5rem;
    background: white;
    border-radius: 50%;
    padding: 0.5rem;
}

.team-mini-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .tournaments-grid {
        grid-template-columns: 1fr;
    }

    .tournament-detail {
        padding: 1rem;
    }

    .tournament-detail-info {
        grid-template-columns: 1fr;
    }

    .tournament-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}