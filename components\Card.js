/**
 * 卡片组件
 */
class Card {
    /**
     * 生成球队卡片
     * @param {Object} team - 球队信息
     * @returns {string} 球队卡片HTML
     */
    static renderTeamCard (team) {
        return `
            <div class="card team-card" data-id="${team.id}">
                <div class="card-header">
                    <h3>${team.name}</h3>
                    <span class="category-badge">${team.category.toUpperCase()}</span>
                </div>
                <div class="card-body">
                    <div class="team-logo">
                        <img src="${team.logo || 'images/logo.png'}" alt="${team.name}">
                    </div>
                    <div class="team-info">
                        <p><strong>地区：</strong>${team.region || '未知'}</p>
                        <p><strong>教练：</strong>${team.coach || '未指定'}</p>
                        <p><strong>简介：</strong>${team.description ? team.description.substring(0, 50) + '...' : '无'}</p>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="teams.html?id=${team.id}" class="view-btn">查看详情</a>
                </div>
            </div>
        `;
    }

    /**
     * 生成比赛卡片
     * @param {Object} match - 比赛信息
     * @param {Object} homeTeam - 主队信息
     * @param {Object} awayTeam - 客队信息
     * @param {Object} tournament - 赛事信息
     * @returns {string} 比赛卡片HTML
     */
    static renderMatchCard (match, homeTeam, awayTeam, tournament) {
        let scoreDisplay = '';
        if (match.status === 'completed' && match.homeScore !== null && match.awayScore !== null) {
            scoreDisplay = `<div class="match-score">${match.homeScore} - ${match.awayScore}</div>`;
        } else {
            scoreDisplay = `<div class="match-time">${match.time}</div>`;
        }

        const statusClass = match.status === 'completed' ? 'completed' :
            match.status === 'live' ? 'live' : 'upcoming';

        return `
            <div class="card match-card">
                <div class="card-header ${statusClass}">
                    <h3>${tournament.name}</h3>
                    <span class="match-date">${formatDate(match.date)}</span>
                </div>
                <div class="card-body">
                    <div class="match-teams">
                        <div class="team home-team">${homeTeam.name}</div>
                        ${scoreDisplay}
                        <div class="team away-team">${awayTeam.name}</div>
                    </div>
                    <p class="match-venue">${match.venue || '未指定场地'}</p>
                </div>
                <div class="card-footer">
                    <a href="matches.html?id=${match.id}" class="view-btn">查看详情</a>
                </div>
            </div>
        `;
    }

    /**
     * 生成赛事卡片
     * @param {Object} tournament - 赛事信息
     * @returns {string} 赛事卡片HTML
     */
    static renderTournamentCard (tournament) {
        return `
            <div class="card tournament-card">
                <div class="card-header">
                    <h3>${tournament.name}</h3>
                </div>
                <div class="card-body">
                    <p><strong>类别：</strong>${tournament.category || '未分类'}</p>
                    <p><strong>时间：</strong>${formatDate(tournament.startDate)} 至 ${formatDate(tournament.endDate)}</p>
                    <p><strong>地点：</strong>${tournament.location || '未指定'}</p>
                </div>
                <div class="card-footer">
                    <a href="tournaments.html?id=${tournament.id}" class="view-btn">查看详情</a>
                </div>
            </div>
        `;
    }
} 