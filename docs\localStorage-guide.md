# HTML5本地存储功能详解

## 📚 项目中的本地存储实现

### 1. **localStorage基础应用**

#### 1.1 用户登录状态管理

```javascript
// Navigation.js - 用户信息存储
static updateUserInfo() {
    const currentUserSpan = document.getElementById('current-user');
    const loginLogoutBtn = document.getElementById('login-logout-btn');
    
    // 从localStorage获取用户信息
    const currentUser = localStorage.getItem('currentUser');
    
    if (currentUser && currentUserSpan && loginLogoutBtn) {
        // 解析JSON数据
        const userData = JSON.parse(currentUser);
        currentUserSpan.textContent = userData.username || '用户';
        
        // 设置退出登录功能
        loginLogoutBtn.innerHTML = '<i class="fas fa-sign-out-alt"></i> 退出登录';
        loginLogoutBtn.href = '#';
        loginLogoutBtn.onclick = () => {
            // 清除用户信息
            localStorage.removeItem('currentUser');
            window.location.reload();
        };
    } else {
        // 未登录状态
        currentUserSpan.textContent = '游客';
        loginLogoutBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录';
        loginLogoutBtn.href = 'login.html';
        loginLogoutBtn.onclick = null;
    }
}

// 用户登录时保存信息
function loginUser(username, userInfo) {
    const userData = {
        username: username,
        loginTime: new Date().toISOString(),
        ...userInfo
    };
    localStorage.setItem('currentUser', JSON.stringify(userData));
}
```

#### 1.2 视频观看次数统计

```javascript
// app.js - 视频观看统计
function incrementViewCount() {
    // 获取当前观看次数
    const currentCount = parseInt(localStorage.getItem('match-video-views') || '0');
    const newCount = currentCount + 1;
    
    // 保存新的观看次数
    localStorage.setItem('match-video-views', newCount.toString());
    
    // 更新页面显示
    const viewCountElement = document.getElementById('view-count');
    if (viewCountElement) {
        viewCountElement.textContent = newCount.toString();
    }
}

// 初始化观看次数显示
function initVideoControls() {
    // 恢复保存的观看次数
    const savedViewCount = localStorage.getItem('match-video-views') || '0';
    const viewCount = document.getElementById('view-count');
    if (viewCount) {
        viewCount.textContent = savedViewCount;
    }
}
```

### 2. **用户偏好设置存储**

#### 2.1 完整的偏好设置管理

```javascript
// teams.html - 用户偏好设置类
class TeamsPageEnhancer {
    initLocalStorage() {
        // 保存用户偏好设置
        const saveUserPreferences = () => {
            const preferences = {
                lastVisited: new Date().toISOString(),
                favoriteTeams: JSON.parse(localStorage.getItem('favoriteTeams') || '[]'),
                viewMode: localStorage.getItem('viewMode') || 'grid',
                filterSettings: this.getCurrentFilterSettings(),
                theme: localStorage.getItem('theme') || 'light'
            };
            localStorage.setItem('userPreferences', JSON.stringify(preferences));
        };
        
        // 页面卸载时自动保存
        window.addEventListener('beforeunload', saveUserPreferences);
        
        // 恢复用户偏好
        this.restoreUserPreferences();
    }
    
    // 恢复用户偏好设置
    restoreUserPreferences() {
        const preferences = JSON.parse(localStorage.getItem('userPreferences') || '{}');
        
        if (preferences.lastVisited) {
            console.log('上次访问时间:', preferences.lastVisited);
        }
        
        if (preferences.viewMode) {
            this.setViewMode(preferences.viewMode);
        }
        
        if (preferences.filterSettings) {
            this.restoreFilterSettings(preferences.filterSettings);
        }
    }
    
    // 获取当前筛选设置
    getCurrentFilterSettings() {
        return {
            category: document.getElementById('team-filter')?.value || 'all',
            region: document.getElementById('region-filter')?.value || 'all',
            sort: document.getElementById('sort-select')?.value || 'name'
        };
    }
    
    // 恢复筛选设置
    restoreFilterSettings(settings) {
        if (settings.category) {
            const categoryFilter = document.getElementById('team-filter');
            if (categoryFilter) categoryFilter.value = settings.category;
        }
        
        if (settings.region) {
            const regionFilter = document.getElementById('region-filter');
            if (regionFilter) regionFilter.value = settings.region;
        }
        
        if (settings.sort) {
            const sortSelect = document.getElementById('sort-select');
            if (sortSelect) sortSelect.value = settings.sort;
        }
    }
}
```

### 3. **收藏功能实现**

#### 3.1 球队收藏管理

```javascript
// 收藏球队功能
class FavoriteManager {
    // 添加收藏
    static addFavorite(teamId, teamInfo) {
        const favorites = this.getFavorites();
        
        // 检查是否已收藏
        if (!favorites.find(fav => fav.id === teamId)) {
            const favoriteItem = {
                id: teamId,
                name: teamInfo.name,
                addedTime: new Date().toISOString(),
                ...teamInfo
            };
            
            favorites.push(favoriteItem);
            localStorage.setItem('favoriteTeams', JSON.stringify(favorites));
            
            console.log('已添加到收藏:', teamInfo.name);
            return true;
        }
        return false;
    }
    
    // 移除收藏
    static removeFavorite(teamId) {
        const favorites = this.getFavorites();
        const updatedFavorites = favorites.filter(fav => fav.id !== teamId);
        
        localStorage.setItem('favoriteTeams', JSON.stringify(updatedFavorites));
        console.log('已从收藏中移除');
    }
    
    // 获取收藏列表
    static getFavorites() {
        return JSON.parse(localStorage.getItem('favoriteTeams') || '[]');
    }
    
    // 检查是否已收藏
    static isFavorite(teamId) {
        const favorites = this.getFavorites();
        return favorites.some(fav => fav.id === teamId);
    }
    
    // 获取收藏数量
    static getFavoriteCount() {
        return this.getFavorites().length;
    }
}
```

### 4. **搜索历史记录**

#### 4.1 搜索记录管理

```javascript
// 搜索历史功能
class SearchHistory {
    static maxHistoryItems = 10; // 最大保存条数
    
    // 添加搜索记录
    static addSearchTerm(term) {
        if (!term || term.trim() === '') return;
        
        const history = this.getSearchHistory();
        const trimmedTerm = term.trim();
        
        // 移除重复项
        const filteredHistory = history.filter(item => item.term !== trimmedTerm);
        
        // 添加新记录到开头
        filteredHistory.unshift({
            term: trimmedTerm,
            timestamp: new Date().toISOString(),
            count: 1
        });
        
        // 限制记录数量
        const limitedHistory = filteredHistory.slice(0, this.maxHistoryItems);
        
        localStorage.setItem('searchHistory', JSON.stringify(limitedHistory));
    }
    
    // 获取搜索历史
    static getSearchHistory() {
        return JSON.parse(localStorage.getItem('searchHistory') || '[]');
    }
    
    // 清除搜索历史
    static clearSearchHistory() {
        localStorage.removeItem('searchHistory');
    }
    
    // 获取热门搜索词
    static getPopularSearchTerms(limit = 5) {
        const history = this.getSearchHistory();
        return history
            .sort((a, b) => b.count - a.count)
            .slice(0, limit)
            .map(item => item.term);
    }
}
```
