/**
 * 中国青少年足球数据平台 - 主应用模块
 *
 * 该模块实现网站的主要功能，包括：
 * - 页面导航和响应式设计
 * - 数据展示和交互
 * - 表单处理和数据管理
 */

// 创建数据管理器实例
const dataManager = new FootballDataManager();

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', function () {
    // 检查URL参数，看是否需要重置数据
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('reset_data')) {
        console.log("重置数据...");
        localStorage.removeItem('data_initialized');
        // 重新加载页面，但不带reset_data参数
        window.location.href = window.location.pathname;
        return;
    }

    // 调试: 输出localStorage中的数据
    console.log("=== 调试信息 ===");
    console.log("localStorage中的tournaments:", JSON.parse(localStorage.getItem(STORAGE_KEYS.TOURNAMENTS) || '[]'));
    console.log("localStorage中的teams:", JSON.parse(localStorage.getItem(STORAGE_KEYS.TEAMS) || '[]'));
    console.log("localStorage中的matches:", JSON.parse(localStorage.getItem(STORAGE_KEYS.MATCHES) || '[]'));
    console.log("localStorage中的players:", JSON.parse(localStorage.getItem(STORAGE_KEYS.PLAYERS) || '[]'));
    console.log("=== 调试结束 ===");

    // 初始化顶部和底部导航高亮
    initActivePage();

    // 初始化搜索功能
    initSearchToggle();

    // 初始化当前页面内容
    initPageContent();
});

/**
 * 初始化底部导航高亮
 */
function initActivePage () {
    const currentPath = window.location.pathname;
    const pageName = currentPath.split('/').pop();

    // 高亮底部导航相应菜单项
    const bottomNavLinks = document.querySelectorAll('.bottom-nav a');
    bottomNavLinks.forEach(link => {
        if (link.getAttribute('href') === pageName) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });

    // 高亮顶部导航相应菜单项
    const topNavLinks = document.querySelectorAll('.top-nav a');
    topNavLinks.forEach(link => {
        if (link.getAttribute('href') === pageName) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

/**
 * 初始化搜索开关功能
 */
function initSearchToggle () {
    const searchToggle = document.getElementById('search-toggle');
    const searchBar = document.querySelector('.search-bar');

    if (searchToggle && searchBar) {
        searchToggle.addEventListener('click', function (e) {
            e.preventDefault();
            searchBar.classList.toggle('active');
            if (searchBar.classList.contains('active')) {
                searchBar.querySelector('input').focus();
            }
        });
    }
}

/**
 * 初始化当前页面内容
 */
function initPageContent () {
    // 获取当前页面路径
    const currentPath = window.location.pathname;
    const pageName = currentPath.split('/').pop();

    // 根据不同页面初始化不同内容
    switch (pageName) {
        case '':
        case 'index.html':
            initHomePage();
            break;
        case 'tournaments.html':
            initTournamentsPage();
            break;
        case 'teams.html':
            initTeamsPage();
            break;
        case 'players.html':
            initPlayersPage();
            break;
        case 'matches.html':
            initMatchesPage();
            break;
        case 'manage.html':
            initManagePage();
            break;
    }
}

/**
 * 初始化首页内容
 */
function initHomePage () {
    // 更新统计数据
    updateStats();

    // 加载最近赛事
    loadRecentTournaments();

    // 加载最近比赛
    loadRecentMatches();
}

/**
 * 更新首页统计数据
 */
function updateStats () {
    const stats = dataManager.getDataStats();

    // 更新统计卡片
    document.getElementById('tournament-count').textContent = stats.tournamentCount;
    document.getElementById('team-count').textContent = stats.teamCount;
    document.getElementById('player-count').textContent = stats.playerCount;
    document.getElementById('match-count').textContent = stats.matchCount;
}

/**
 * 加载最近赛事到首页
 */
function loadRecentTournaments () {
    const tournaments = dataManager.getTournaments();
    const recentTournamentsContainer = document.getElementById('recent-tournaments');

    if (!recentTournamentsContainer) return;

    // 按开始日期排序（最近的在前）
    const sortedTournaments = tournaments.sort((a, b) => {
        return new Date(b.startDate) - new Date(a.startDate);
    }).slice(0, 3); // 只显示前3个

    recentTournamentsContainer.innerHTML = '';

    if (sortedTournaments.length === 0) {
        recentTournamentsContainer.innerHTML = '<p class="no-data">暂无赛事数据</p>';
        return;
    }

    // 遍历赛事并创建卡片
    sortedTournaments.forEach(tournament => {
        const card = document.createElement('div');
        card.className = 'card';
        card.innerHTML = `
            <div class="card-header">
                <h3>${tournament.name}</h3>
            </div>
            <div class="card-body">
                <p><strong>类别：</strong>${tournament.category}</p>
                <p><strong>时间：</strong>${formatDate(tournament.startDate)} 至 ${formatDate(tournament.endDate)}</p>
                <p><strong>地点：</strong>${tournament.location}</p>
            </div>
            <div class="card-footer">
                <a href="tournaments.html?id=${tournament.id}">查看详情</a>
            </div>
        `;
        recentTournamentsContainer.appendChild(card);
    });
}

/**
 * 加载最近比赛到首页
 */
function loadRecentMatches () {
    const matches = dataManager.getMatches();
    const recentMatchesContainer = document.getElementById('recent-matches');

    if (!recentMatchesContainer) return;

    // 按日期排序（未来的比赛优先，然后是最近的比赛）
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const upcomingMatches = matches.filter(match => new Date(match.date) >= today)
        .sort((a, b) => new Date(a.date) - new Date(b.date));

    const recentCompletedMatches = matches.filter(match =>
        new Date(match.date) < today && match.status === 'completed'
    ).sort((a, b) => new Date(b.date) - new Date(a.date));

    // 组合最近的比赛，优先显示即将到来的比赛
    const recentMatches = [...upcomingMatches, ...recentCompletedMatches].slice(0, 3);

    recentMatchesContainer.innerHTML = '';

    if (recentMatches.length === 0) {
        recentMatchesContainer.innerHTML = '<p class="no-data">暂无比赛数据</p>';
        return;
    }

    // 遍历比赛并创建卡片
    recentMatches.forEach(match => {
        const homeTeam = dataManager.getTeamById(match.homeTeamId);
        const awayTeam = dataManager.getTeamById(match.awayTeamId);
        const tournament = dataManager.getTournamentById(match.tournamentId);

        if (!homeTeam || !awayTeam || !tournament) return;

        const card = document.createElement('div');
        card.className = 'card';

        let scoreDisplay = '';
        if (match.status === 'completed' && match.homeScore !== null && match.awayScore !== null) {
            scoreDisplay = `<div class="match-score">${match.homeScore} - ${match.awayScore}</div>`;
        } else {
            scoreDisplay = `<div class="match-time">${match.time}</div>`;
        }

        card.innerHTML = `
            <div class="card-header">
                <h3>${tournament.name}</h3>
            </div>
            <div class="card-body">
                <div class="match-teams">
                    <div class="team home-team">${homeTeam.name}</div>
                    ${scoreDisplay}
                    <div class="team away-team">${awayTeam.name}</div>
                </div>
                <p class="match-info">
                    <span class="match-date">${formatDate(match.date)}</span>
                    <span class="match-venue">${match.venue}</span>
                </p>
            </div>
            <div class="card-footer">
                <a href="matches.html?id=${match.id}">查看详情</a>
            </div>
        `;
        recentMatchesContainer.appendChild(card);
    });
}

/**
 * 初始化赛事页面
 */
function initTournamentsPage () {
    console.log("初始化赛事页面...");

    try {
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const tournamentId = urlParams.get('id');

        // 如果有ID参数，显示单个赛事详情
        if (tournamentId) {
            console.log("显示赛事详情:", tournamentId);
            // 确认赛事存在
            const tournament = dataManager.getTournamentById(tournamentId);
            if (!tournament) {
                console.error(`未找到赛事: ${tournamentId}`);
                // 如果找不到赛事，显示错误并加载所有赛事列表
                const detailContainer = document.querySelector('.tournament-detail');
                if (detailContainer) {
                    detailContainer.innerHTML = '<p class="no-data">未找到赛事信息</p>';
                    detailContainer.style.display = 'block';
                }
                return;
            }

            showTournamentDetail(tournamentId);
        } else {
            // 否则显示所有赛事列表
            console.log("加载所有赛事列表");

            // 初始化搜索栏
            const searchContainer = document.querySelector('.search-container');
            if (searchContainer) {
                searchContainer.innerHTML = `
                    <div class="search-bar">
                        <input type="text" id="tournament-search" placeholder="搜索赛事...">
                        <button id="search-btn"><i class="fas fa-search"></i></button>
                    </div>
                `;

                // 初始化搜索功能
                initTournamentSearch();
            }

            // 加载赛事列表
            loadTournaments();
        }
    } catch (error) {
        console.error("初始化赛事页面出错:", error);
        // 在页面上显示错误信息
        const container = document.querySelector('.tournaments-grid');
        if (container) {
            container.innerHTML = `<p class="no-data">加载赛事数据失败: ${error.message}</p>`;
        }
    }
}

/**
 * 加载赛事列表
 */
function loadTournaments () {
    const tournamentsGrid = document.querySelector('.tournaments-grid');
    const tournaments = dataManager.getTournaments();

    if (!tournaments || tournaments.length === 0) {
        tournamentsGrid.innerHTML = '<p class="no-data">暂无赛事数据</p>';
        return;
    }

    tournamentsGrid.innerHTML = tournaments.map(tournament => `
        <div class="tournament-card">
            <div class="card-header">
                <h3>${tournament.name}</h3>
            </div>
            <div class="card-body">
                <div class="tournament-info">
                    <p><strong>类别：</strong>${tournament.category}</p>
                    <p><strong>时间：</strong>${tournament.startDate} - ${tournament.endDate}</p>
                    <p><strong>地点：</strong>${tournament.location}</p>
                </div>
                <div class="tournament-stats">
                    <div class="tournament-stat">
                        <div class="stat-value">${tournament.teams && tournament.teams.length || 0}</div>
                        <div class="stat-label">参赛队伍</div>
                    </div>
                    <div class="tournament-stat">
                        <div class="stat-value">${tournament.matches && tournament.matches.length || 0}</div>
                        <div class="stat-label">比赛场数</div>
                    </div>
                    <div class="tournament-stat">
                        <div class="stat-value">${tournament.status === 'ongoing' ? '进行中' : tournament.status === 'completed' ? '已结束' : '未开始'}</div>
                        <div class="stat-label">状态</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary" onclick="showTournamentDetail('${tournament.id}')">查看详情</button>
            </div>
        </div>
    `).join('');
}

/**
 * 显示赛事详情
 */
function showTournamentDetail (tournamentId) {
    const tournament = dataManager.getTournamentById(tournamentId);
    const detailContainer = document.querySelector('.tournament-detail');
    const tournamentsGrid = document.querySelector('.tournaments-grid');

    if (!tournament) {
        alert('未找到赛事信息');
        return;
    }

    // 隐藏赛事列表，显示详情
    tournamentsGrid.style.display = 'none';
    detailContainer.style.display = 'block';

    // 获取相关比赛和球队信息
    const matches = Array.isArray(tournament.matches)
        ? tournament.matches.map(matchId => dataManager.getMatchById(matchId)).filter(Boolean)
        : [];
    const teams = Array.isArray(tournament.teams)
        ? tournament.teams.map(teamId => dataManager.getTeamById(teamId)).filter(Boolean)
        : [];

    detailContainer.innerHTML = `
        <div class="tournament-detail-header">
            <h2>${tournament.name}</h2>
        </div>
        <div class="tournament-detail-info">
            <div class="tournament-detail-section">
                <h3>基本信息</h3>
                <p><strong>类别：</strong>${tournament.category || '未分类'}</p>
                <p><strong>时间：</strong>${tournament.startDate || '未设置'} - ${tournament.endDate || '未设置'}</p>
                <p><strong>地点：</strong>${tournament.location || '未指定'}</p>
                <p><strong>状态：</strong>${tournament.status === 'ongoing' ? '进行中' : tournament.status === 'completed' ? '已结束' : '未开始'}</p>
            </div>
            <div class="tournament-detail-section">
                <h3>赛事简介</h3>
                <p>${tournament.description || '暂无简介'}</p>
            </div>
        </div>

        <div class="tournament-matches">
            <h3>比赛列表${matches.length ? ` (${matches.length})` : ''}</h3>
            ${matches.length > 0 ?
            `<div class="matches-grid">
                    ${matches.map(match => {
                // 安全地获取主队和客队信息
                const homeTeamId = match.homeTeam || match.homeTeamId;
                const awayTeamId = match.awayTeam || match.awayTeamId;
                const homeTeam = teams.find(t => t.id === homeTeamId);
                const awayTeam = teams.find(t => t.id === awayTeamId);

                return `
                        <div class="match-card" onclick="showMatchDetail('${match.id}')">
                            <div class="match-teams">
                                <div class="team">
                                    <img src="${homeTeam?.logo || 'images/logo.png'}" alt="${homeTeam?.name || '未知球队'}">
                                    <span>${homeTeam?.name || '未知球队'}</span>
                                </div>
                                <div class="match-score">
                                    <span>${match.homeScore ?? 0} - ${match.awayScore ?? 0}</span>
                                </div>
                                <div class="team">
                                    <img src="${awayTeam?.logo || 'images/logo.png'}" alt="${awayTeam?.name || '未知球队'}">
                                    <span>${awayTeam?.name || '未知球队'}</span>
                                </div>
                            </div>
                            <div class="match-info">
                                <span>${formatDate(match.date)}</span>
                                <span>${match.time || '--:--'}</span>
                                <span>${match.venue || '未指定场地'}</span>
                            </div>
                        </div>
                    `}).join('')}
                </div>` :
            '<p class="no-data">暂无比赛</p>'
        }
        </div>

        <div class="tournament-teams">
            <h3>参赛队伍${teams.length ? ` (${teams.length})` : ''}</h3>
            ${teams.length > 0 ?
            `<div class="teams-grid">
                    ${teams.map(team => `
                        <div class="team-mini-card" onclick="showTeamDetail('${team.id}')">
                            <div class="team-mini-logo">
                                <img src="${team.logo || 'images/logo.png'}" alt="${team.name}">
                            </div>
                            <h4>${team.name}</h4>
                        </div>
                    `).join('')}
                </div>` :
            '<p class="no-data">暂无参赛队伍</p>'
        }
        </div>

        <div class="actions" style="margin-top: 2rem; text-align: center;">
            <button class="btn btn-secondary" onclick="backToTournamentList()">返回列表</button>
        </div>
    `;
}

/**
 * 返回赛事列表
 */
function backToTournamentList () {
    const detailContainer = document.querySelector('.tournament-detail');
    const tournamentsGrid = document.querySelector('.tournaments-grid');

    detailContainer.style.display = 'none';
    tournamentsGrid.style.display = 'grid';

    // 更新URL，移除赛事ID
    const url = new URL(window.location.href);
    url.searchParams.delete('id');
    window.history.pushState({}, '', url);
}

/**
 * 过滤赛事
 */
function filterTournaments (category) {
    const tournaments = dataManager.getTournaments();
    const filteredTournaments = category === 'all'
        ? tournaments
        : tournaments.filter(t => t.category === category);

    const tournamentsGrid = document.querySelector('.tournaments-grid');

    if (!filteredTournaments || filteredTournaments.length === 0) {
        tournamentsGrid.innerHTML = '<p class="no-data">没有找到符合条件的赛事</p>';
        return;
    }

    tournamentsGrid.innerHTML = filteredTournaments.map(tournament => `
        <div class="tournament-card">
            <div class="card-header">
                <h3>${tournament.name}</h3>
            </div>
            <div class="card-body">
                <div class="tournament-info">
                    <p><strong>类别：</strong>${tournament.category}</p>
                    <p><strong>时间：</strong>${tournament.startDate} - ${tournament.endDate}</p>
                    <p><strong>地点：</strong>${tournament.location}</p>
                </div>
                <div class="tournament-stats">
                    <div class="tournament-stat">
                        <div class="stat-value">${tournament.teams && tournament.teams.length || 0}</div>
                        <div class="stat-label">参赛队伍</div>
                    </div>
                    <div class="tournament-stat">
                        <div class="stat-value">${tournament.matches && tournament.matches.length || 0}</div>
                        <div class="stat-label">比赛场数</div>
                    </div>
                    <div class="tournament-stat">
                        <div class="stat-value">${tournament.status === 'ongoing' ? '进行中' : tournament.status === 'completed' ? '已结束' : '未开始'}</div>
                        <div class="stat-label">状态</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary" onclick="showTournamentDetail('${tournament.id}')">查看详情</button>
            </div>
        </div>
    `).join('');
}

/**
 * 球队页面初始化
 */
function initTeamsPage () {
    // 初始化搜索功能
    initTeamSearch();

    // 初始化筛选功能
    initTeamFilter();

    // 加载球队列表
    loadTeams();

    // 检查URL参数，如果有ID则显示详情
    const urlParams = new URLSearchParams(window.location.search);
    const teamId = urlParams.get('id');
    if (teamId) {
        showTeamDetail(teamId);
    }
}

/**
 * 初始化球队搜索功能
 */
function initTeamSearch () {
    const searchInput = document.getElementById('team-search');
    const searchBtn = document.getElementById('search-btn');

    if (searchInput && searchBtn) {
        searchBtn.addEventListener('click', function () {
            const searchTerm = searchInput.value.trim().toLowerCase();
            searchTeams(searchTerm);
        });

        searchInput.addEventListener('keyup', function (e) {
            if (e.key === 'Enter') {
                const searchTerm = searchInput.value.trim().toLowerCase();
                searchTeams(searchTerm);
            }
        });
    }
}

/**
 * 搜索球队
 */
function searchTeams (searchTerm) {
    const filterSelect = document.getElementById('team-filter');
    const category = filterSelect ? filterSelect.value : 'all';
    filterTeams(category, searchTerm);
}

/**
 * 初始化球队筛选功能
 */
function initTeamFilter () {
    console.log("初始化球队筛选功能");
    const filterSelect = document.getElementById('team-filter');

    if (filterSelect) {
        filterSelect.addEventListener('change', function () {
            const searchTerm = document.getElementById('team-search').value.trim();
            filterTeams(this.value, searchTerm);
        });
    }
}

/**
 * 筛选球队 - 增强版支持多重筛选和排序
 */
function filterTeams (category = 'all', searchTerm = '', region = 'all', sortBy = 'name') {
    console.log("筛选球队, 类别:", category, "搜索词:", searchTerm, "地区:", region, "排序:", sortBy);

    try {
        // 获取所有球队
        let teams = dataManager.getTeams();

        // 应用类别筛选
        if (category !== 'all') {
            teams = teams.filter(team => team.category.toLowerCase() === category.toLowerCase());
        }

        // 应用地区筛选
        if (region !== 'all') {
            teams = teams.filter(team => team.region === region);
        }

        // 应用搜索筛选
        if (searchTerm) {
            const searchLower = searchTerm.toLowerCase();
            teams = teams.filter(team =>
                team.name.toLowerCase().includes(searchLower) ||
                team.region.toLowerCase().includes(searchLower) ||
                (team.description && team.description.toLowerCase().includes(searchLower)) ||
                (team.coach && team.coach.toLowerCase().includes(searchLower))
            );
        }

        // 应用排序
        switch (sortBy) {
            case 'name':
                teams.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'));
                break;
            case 'founded':
                teams.sort((a, b) => {
                    const yearA = parseInt(a.foundYear) || 0;
                    const yearB = parseInt(b.foundYear) || 0;
                    return yearB - yearA; // 新成立的在前
                });
                break;
            case 'region':
                teams.sort((a, b) => a.region.localeCompare(b.region, 'zh-CN'));
                break;
            default:
                teams.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'));
        }

        // 获取容器
        const container = document.getElementById('teams-container');
        const noDataMessage = document.getElementById('no-teams-message');

        if (!container) {
            console.error("找不到teams-container元素");
            return;
        }

        container.innerHTML = '';

        // 检查是否有结果
        if (teams.length === 0) {
            container.style.display = 'none';
            if (noDataMessage) {
                noDataMessage.style.display = 'block';
                noDataMessage.innerHTML = `
                    <i class="fas fa-users-slash"></i>
                    <p>没有找到匹配的球队</p>
                    <small>尝试调整筛选条件或搜索关键词</small>
                    <button class="btn btn-primary" onclick="resetFilters()">重置筛选</button>
                `;
            }
            return;
        }

        // 隐藏无数据消息，显示球队列表
        container.style.display = 'grid';
        if (noDataMessage) {
            noDataMessage.style.display = 'none';
        }

        // 遍历球队并创建卡片
        teams.forEach((team, index) => {
            const card = document.createElement('div');
            card.className = 'card team-card';
            card.dataset.id = team.id;

            // 添加动画延迟
            card.style.animationDelay = `${index * 0.1}s`;

            card.innerHTML = `
                <div class="card-header">
                    <h3>${team.name}</h3>
                    <span class="category-badge">${team.category.toUpperCase()}</span>
                </div>
                <div class="card-body">
                    <div class="team-logo">
                        <img src="${team.logo || 'images/logo.png'}" alt="${team.name}"
                             onerror="this.src='images/logo.png'" loading="lazy">
                    </div>
                    <div class="team-info">
                        <p><strong>地区：</strong>${team.region || '未知'}</p>
                        <p><strong>教练：</strong>${team.coach || '未指定'}</p>
                        <p><strong>成立：</strong>${team.foundYear || '未知'}</p>
                        <p><strong>简介：</strong>${team.description ? team.description.substring(0, 50) + '...' : '无'}</p>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="teams.html?id=${team.id}" class="view-btn"
                       onclick="saveScrollPosition()">查看详情</a>
                </div>
            `;

            // 添加点击事件
            card.addEventListener('click', function (e) {
                if (!e.target.closest('.view-btn')) {
                    window.location.href = `teams.html?id=${team.id}`;
                }
            });

            container.appendChild(card);
        });

        // 显示筛选结果统计
        updateFilterStats(teams.length, dataManager.getTeams().length);

    } catch (error) {
        console.error("筛选球队失败:", error);
        const container = document.getElementById('teams-container');
        if (container) {
            container.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>筛选球队失败: ${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }
}

/**
 * 更新筛选结果统计
 */
function updateFilterStats (filteredCount, totalCount) {
    const statsElement = document.getElementById('filter-stats');
    if (!statsElement) {
        // 创建统计元素
        const stats = document.createElement('div');
        stats.id = 'filter-stats';
        stats.className = 'filter-stats';

        const container = document.getElementById('teams-container');
        if (container && container.parentNode) {
            container.parentNode.insertBefore(stats, container);
        }
    }

    const statsEl = document.getElementById('filter-stats');
    if (statsEl) {
        statsEl.innerHTML = `
            <span class="stats-text">
                显示 <strong>${filteredCount}</strong> 个球队，共 <strong>${totalCount}</strong> 个
            </span>
        `;
    }
}

/**
 * 重置筛选条件
 */
function resetFilters () {
    const teamFilter = document.getElementById('team-filter');
    const regionFilter = document.getElementById('region-filter');
    const sortSelect = document.getElementById('sort-select');
    const searchInput = document.getElementById('team-search');

    if (teamFilter) teamFilter.value = 'all';
    if (regionFilter) regionFilter.value = 'all';
    if (sortSelect) sortSelect.value = 'name';
    if (searchInput) searchInput.value = '';

    // 重新加载球队列表
    filterTeams();
}

/**
 * 保存滚动位置
 */
function saveScrollPosition () {
    sessionStorage.setItem('teamsScrollPosition', window.scrollY);
}

/**
 * 恢复滚动位置
 */
function restoreScrollPosition () {
    const scrollPosition = sessionStorage.getItem('teamsScrollPosition');
    if (scrollPosition) {
        window.scrollTo(0, parseInt(scrollPosition));
        sessionStorage.removeItem('teamsScrollPosition');
    }
}

/**
 * 加载球队列表
 */
function loadTeams () {
    console.log("加载球队列表...");

    // 获取当前筛选值和搜索词
    const filterSelect = document.getElementById('team-filter');
    const searchInput = document.getElementById('team-search');

    const category = filterSelect ? filterSelect.value : 'all';
    const searchTerm = searchInput ? searchInput.value.trim() : '';

    // 使用筛选功能加载球队
    filterTeams(category, searchTerm);
}

/**
 * 显示球队详情
 */
function showTeamDetail (teamId) {
    console.log("显示球队详情:", teamId);

    try {
        // 检查数据管理器是否存在
        if (!dataManager) {
            console.error('数据管理器未初始化');
            alert('数据管理器未初始化，请刷新页面重试');
            return;
        }

        // 获取球队信息
        const team = dataManager.getTeamById(teamId);
        if (!team) {
            console.error("找不到球队信息:", teamId);
            alert('未找到球队信息');
            return;
        }

        // 获取该球队的球员
        const players = dataManager.getPlayersByTeamId(teamId);

        // 获取该球队参与的比赛
        const matches = dataManager.getMatchesByTeamId(teamId);

        // 隐藏球队列表
        const teamsGrid = document.querySelector('.teams-grid');
        if (teamsGrid) {
            teamsGrid.style.display = 'none';
        }

        // 隐藏搜索栏和筛选器
        const searchBar = document.querySelector('.search-bar');
        const filterSection = document.querySelector('.filter-section');

        if (searchBar) searchBar.style.display = 'none';
        if (filterSection) filterSection.style.display = 'none';

        // 更新页面标题
        const pageTitle = document.querySelector('.page-header h1') || document.querySelector('.page-header h2');
        if (pageTitle) {
            pageTitle.innerHTML = `<i class="fas fa-users"></i> 球队详情`;
        }

        // 获取详情容器
        const detailContainer = document.getElementById('team-detail');
        if (!detailContainer) {
            console.error('找不到team-detail容器');
            return;
        }
        detailContainer.style.display = 'block';

        // 计算球队战绩
        let wins = 0;
        let draws = 0;
        let losses = 0;

        matches.forEach(match => {
            if (match.status === 'completed') {
                const isHomeTeam = match.homeTeamId === teamId;
                const homeScore = match.homeScore || 0;
                const awayScore = match.awayScore || 0;

                if (homeScore === awayScore) {
                    draws++;
                } else if ((isHomeTeam && homeScore > awayScore) || (!isHomeTeam && awayScore > homeScore)) {
                    wins++;
                } else {
                    losses++;
                }
            }
        });

        // 更新详情内容 - 修复布局问题
        detailContainer.innerHTML = `
            <div class="detail-header">
                <button class="back-btn" onclick="window.location.href='teams.html'">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </button>
            </div>

            <div class="team-detail-header">
                <div class="team-basic-info">
                    <div class="team-detail-logo">
                        <img src="${team.logo || 'images/logo.png'}" alt="${team.name}" onerror="this.src='images/logo.png'">
                    </div>
                    <div class="team-detail-info">
                        <h2>${team.name}</h2>
                    </div>
                </div>

                <div class="team-meta">
                    <div class="team-meta-item">
                        <div class="meta-label">类别</div>
                        <div class="meta-value">${team.category || '未分类'}</div>
                    </div>
                    <div class="team-meta-item">
                        <div class="meta-label">地区</div>
                        <div class="meta-value">${team.region || '未知'}</div>
                    </div>
                    <div class="team-meta-item">
                        <div class="meta-label">成立时间</div>
                        <div class="meta-value">${team.foundYear || '未知'}</div>
                    </div>
                    <div class="team-meta-item">
                        <div class="meta-label">教练</div>
                        <div class="meta-value">${team.coach || '未指定'}</div>
                    </div>
                </div>

                ${team.description ? `
                    <div class="team-description">
                        <h3>球队简介</h3>
                        <p>${team.description}</p>
                    </div>
                ` : ''}
            </div>

            <div class="team-stats">
                <div class="team-stat-card">
                    <div class="stat-label">比赛</div>
                    <div class="stat-value">${wins + draws + losses}</div>
                </div>
                <div class="team-stat-card">
                    <div class="stat-label">胜</div>
                    <div class="stat-value">${wins}</div>
                </div>
                <div class="team-stat-card">
                    <div class="stat-label">平</div>
                    <div class="stat-value">${draws}</div>
                </div>
                <div class="team-stat-card">
                    <div class="stat-label">负</div>
                    <div class="stat-value">${losses}</div>
                </div>
            </div>

            <div class="team-players">
                <h3>球员名单${players.length ? ` (${players.length})` : ''}</h3>
                ${players.length > 0 ? `
                    <div class="players-grid">
                        ${players.map(player => `
                            <div class="player-card">
                                <h4>${player.name}</h4>
                                <div class="player-info">
                                    <p><strong>位置：</strong>${player.position}</p>
                                    <p><strong>生日：</strong>${formatDate(player.birthDate)}</p>
                                    ${player.height ? `<p><strong>身高：</strong>${player.height}cm</p>` : ''}
                                    ${player.weight ? `<p><strong>体重：</strong>${player.weight}kg</p>` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : '<p class="no-data">暂无球员数据</p>'}
            </div>

            <div class="team-matches">
                <h3>最近比赛${matches.length ? ` (${matches.length})` : ''}</h3>
                ${matches.length > 0 ? `
                    <div class="matches-list">
                        ${matches.map(match => {
            // 获取对手信息
            const opponent = dataManager.getTeamById(match.homeTeamId === teamId ? match.awayTeamId : match.homeTeamId);
            const tournament = dataManager.getTournamentById(match.tournamentId);

            let result = '';
            const isHomeTeam = match.homeTeamId === teamId;

            if (match.status === 'completed') {
                const homeScore = match.homeScore || 0;
                const awayScore = match.awayScore || 0;

                if (homeScore === awayScore) {
                    result = '<span class="result draw">平</span>';
                } else if ((isHomeTeam && homeScore > awayScore) || (!isHomeTeam && awayScore > homeScore)) {
                    result = '<span class="result win">胜</span>';
                } else {
                    result = '<span class="result loss">负</span>';
                }
            }

            return `
                                <div class="match-item" onclick="window.location.href='matches.html?id=${match.id}'">
                                    <div class="match-item-header">
                                        <span class="tournament-name">${tournament ? tournament.name : '未知赛事'}</span>
                                        <span class="match-date">${formatDate(match.date)}</span>
                                    </div>
                                    <div class="match-item-content">
                                        ${isHomeTeam ? `
                                            <span class="team-name home">${team.name}</span>
                                            <span class="match-score">${match.status === 'completed' ? `${match.homeScore || 0} - ${match.awayScore || 0}` : match.time}</span>
                                            <span class="team-name away">${opponent ? opponent.name : '未知球队'}</span>
                                        ` : `
                                            <span class="team-name home">${opponent ? opponent.name : '未知球队'}</span>
                                            <span class="match-score">${match.status === 'completed' ? `${match.homeScore || 0} - ${match.awayScore || 0}` : match.time}</span>
                                            <span class="team-name away">${team.name}</span>
                                        `}
                                        ${result}
                                    </div>
                                </div>
                            `;
        }).join('')}
                    </div>
                ` : '<p class="no-data">暂无比赛数据</p>'}
            </div>
        `;

    } catch (error) {
        console.error("显示球队详情失败:", error);
        alert('加载球队详情时发生错误');
    }
}

/**
 * 球员页面初始化
 */
function initPlayersPage () {
    // 在这里实现球员页面的初始化功能
}

/**
 * 比赛页面初始化
 */
function initMatchesPage () {
    console.log("初始化比赛页面...");

    try {
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const matchId = urlParams.get('id');

        if (matchId) {
            console.log("查看比赛详情:", matchId);
            showMatchDetail(matchId);
        } else {
            // 初始化搜索和筛选功能
            initMatchSearch();
            initMatchFilter();

            // 加载比赛列表
            loadMatches();
        }
    } catch (error) {
        console.error("初始化比赛页面出错:", error);
    }
}

/**
 * 初始化比赛搜索功能
 */
function initMatchSearch () {
    console.log("初始化比赛搜索功能");
    const searchInput = document.getElementById('match-search');
    const searchBtn = document.getElementById('search-btn');

    if (searchInput && searchBtn) {
        searchBtn.addEventListener('click', function () {
            const searchTerm = searchInput.value.trim();
            const filterValue = document.getElementById('match-filter').value;
            loadMatches(filterValue, searchTerm);
        });

        searchInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                const searchTerm = searchInput.value.trim();
                const filterValue = document.getElementById('match-filter').value;
                loadMatches(filterValue, searchTerm);
            }
        });
    }
}

/**
 * 初始化比赛筛选功能
 */
function initMatchFilter () {
    console.log("初始化比赛筛选功能");
    const filterSelect = document.getElementById('match-filter');

    if (filterSelect) {
        filterSelect.addEventListener('change', function () {
            const searchTerm = document.getElementById('match-search').value.trim();
            loadMatches(this.value, searchTerm);
        });
    }
}

/**
 * 加载比赛列表
 */
function loadMatches (filter = 'all', searchQuery = '') {
    console.log("加载比赛列表, 筛选:", filter, "搜索:", searchQuery);

    try {
        // 获取所有比赛
        let matches = dataManager.getMatches();
        console.log("原始比赛数量:", matches.length);

        // 应用筛选
        switch (filter) {
            case 'upcoming':
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                matches = matches.filter(match =>
                    new Date(match.date) >= today && match.status !== 'completed'
                );
                break;
            case 'live':
                const now = new Date();
                matches = matches.filter(match => match.status === 'live');
                break;
            case 'finished':
                matches = matches.filter(match => match.status === 'completed');
                break;
        }

        // 应用搜索
        if (searchQuery) {
            searchQuery = searchQuery.toLowerCase();
            matches = matches.filter(match => {
                const homeTeam = dataManager.getTeamById(match.homeTeamId);
                const awayTeam = dataManager.getTeamById(match.awayTeamId);
                const tournament = dataManager.getTournamentById(match.tournamentId);

                return (
                    (homeTeam && homeTeam.name.toLowerCase().includes(searchQuery)) ||
                    (awayTeam && awayTeam.name.toLowerCase().includes(searchQuery)) ||
                    (tournament && tournament.name.toLowerCase().includes(searchQuery)) ||
                    (match.venue && match.venue.toLowerCase().includes(searchQuery))
                );
            });
        }

        console.log("筛选后比赛数量:", matches.length);

        // 获取容器
        const container = document.getElementById('matches-container');
        if (!container) {
            console.error("找不到matches-container元素");
            return;
        }

        container.innerHTML = '';

        // 检查是否有结果
        if (matches.length === 0) {
            container.innerHTML = '<p class="no-data">没有找到匹配的比赛</p>';
            return;
        }

        // 按日期排序（最近的在前）
        matches.sort((a, b) => new Date(b.date) - new Date(a.date));

        // 遍历比赛并创建卡片
        matches.forEach(match => {
            const homeTeam = dataManager.getTeamById(match.homeTeamId);
            const awayTeam = dataManager.getTeamById(match.awayTeamId);
            const tournament = dataManager.getTournamentById(match.tournamentId);

            if (!homeTeam || !awayTeam || !tournament) {
                console.warn(`无法加载比赛完整数据: ${match.id}`);
                return;
            }

            const card = document.createElement('div');
            card.className = 'card match-card';

            let scoreDisplay = '';
            if (match.status === 'completed' && match.homeScore !== null && match.awayScore !== null) {
                scoreDisplay = `<div class="match-score">${match.homeScore} - ${match.awayScore}</div>`;
            } else {
                scoreDisplay = `<div class="match-time">${match.time}</div>`;
            }

            const statusClass = match.status === 'completed' ? 'completed' :
                match.status === 'live' ? 'live' : 'upcoming';

            card.innerHTML = `
                <div class="card-header ${statusClass}">
                    <h3>${tournament.name}</h3>
                    <span class="match-date">${formatDate(match.date)}</span>
                </div>
                <div class="card-body">
                    <div class="match-teams">
                        <div class="team home-team">${homeTeam.name}</div>
                        ${scoreDisplay}
                        <div class="team away-team">${awayTeam.name}</div>
                    </div>
                    <p class="match-venue">${match.venue || '未指定场地'}</p>
                </div>
                <div class="card-footer">
                    <a href="matches.html?id=${match.id}" class="view-btn">查看详情</a>
                </div>
            `;

            container.appendChild(card);
        });

    } catch (error) {
        console.error("加载比赛数据失败:", error);
        const container = document.getElementById('matches-container');
        if (container) {
            container.innerHTML = `<p class="no-data">加载比赛数据失败: ${error.message}</p>`;
        }
    }
}

/**
 * 显示比赛详情
 */
function showMatchDetail (matchId) {
    console.log("显示比赛详情:", matchId);

    try {
        // 获取比赛信息
        const match = dataManager.getMatchById(matchId);
        if (!match) {
            console.error("找不到比赛信息:", matchId);
            document.getElementById('match-detail').innerHTML =
                '<p class="no-data">找不到比赛信息</p>';
            return;
        }

        // 获取相关信息
        const homeTeam = dataManager.getTeamById(match.homeTeamId);
        const awayTeam = dataManager.getTeamById(match.awayTeamId);
        const tournament = dataManager.getTournamentById(match.tournamentId);

        if (!homeTeam || !awayTeam || !tournament) {
            console.error("无法加载比赛完整数据");
            document.getElementById('match-detail').innerHTML =
                '<p class="no-data">数据加载错误</p>';
            return;
        }

        // 隐藏比赛列表
        const matchesList = document.querySelector('.matches-list');
        if (matchesList) {
            matchesList.style.display = 'none';
        }

        // 隐藏搜索栏和筛选器
        const searchBar = document.querySelector('.search-bar');
        const filterSection = document.querySelector('.filter-section');

        if (searchBar) searchBar.style.display = 'none';
        if (filterSection) filterSection.style.display = 'none';

        // 更新页面标题
        document.querySelector('.page-header h2').innerHTML =
            `<i class="fas fa-futbol"></i> 比赛详情`;

        // 获取详情容器
        const detailContainer = document.getElementById('match-detail');

        // 构建比赛状态显示
        let statusDisplay = '';
        if (match.status === 'completed') {
            statusDisplay = '<span class="status completed">已结束</span>';
        } else if (match.status === 'live') {
            statusDisplay = '<span class="status live">进行中</span>';
        } else {
            statusDisplay = '<span class="status upcoming">未开始</span>';
        }

        // 构建比分显示
        let scoreDisplay = '';
        if (match.status === 'completed' && match.homeScore !== null && match.awayScore !== null) {
            scoreDisplay = `
                <div class="match-score-large">
                    <span class="score home-score">${match.homeScore}</span>
                    <span class="score-divider">-</span>
                    <span class="score away-score">${match.awayScore}</span>
                </div>
            `;
        } else if (match.status === 'live' && match.homeScore !== null && match.awayScore !== null) {
            scoreDisplay = `
                <div class="match-score-large live">
                    <span class="score home-score">${match.homeScore}</span>
                    <span class="score-divider">-</span>
                    <span class="score away-score">${match.awayScore}</span>
                </div>
            `;
        } else {
            scoreDisplay = `
                <div class="match-time-large">
                    ${match.time}
                </div>
            `;
        }

        // 更新详情内容
        detailContainer.innerHTML = `
            <div class="detail-header">
                <button class="back-btn" onclick="window.location.href='matches.html'">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </button>
                ${statusDisplay}
            </div>

            <div class="detail-tournament">
                <h3>${tournament.name}</h3>
                <p class="match-date-time">${formatDate(match.date)} ${match.time}</p>
                <p class="match-venue">${match.venue || '未指定场地'}</p>
            </div>

            <div class="match-detail-content">
                <div class="teams-container">
                    <div class="team-detail home-team">
                        <div class="team-logo">
                            <img src="${homeTeam.logo || 'images/logo.png'}" alt="${homeTeam.name}">
                        </div>
                        <h3 class="team-name">${homeTeam.name}</h3>
                        <p class="team-category">${homeTeam.category || ''}</p>
                    </div>

                    ${scoreDisplay}

                    <div class="team-detail away-team">
                        <div class="team-logo">
                            <img src="${awayTeam.logo || 'images/logo.png'}" alt="${awayTeam.name}">
                        </div>
                        <h3 class="team-name">${awayTeam.name}</h3>
                        <p class="team-category">${awayTeam.category || ''}</p>
                    </div>
                </div>

                <div class="match-info-container">
                    <h4>比赛信息</h4>

                    <div class="match-info-item">
                        <div class="info-label">比赛日期</div>
                        <div class="info-value">${formatDate(match.date)}</div>
                    </div>

                    <div class="match-info-item">
                        <div class="info-label">比赛时间</div>
                        <div class="info-value">${match.time}</div>
                    </div>

                    <div class="match-info-item">
                        <div class="info-label">比赛场地</div>
                        <div class="info-value">${match.venue || '未指定'}</div>
                    </div>

                    <div class="match-info-item">
                        <div class="info-label">所属赛事</div>
                        <div class="info-value">
                            <a href="tournaments.html?id=${tournament.id}">${tournament.name}</a>
                        </div>
                    </div>

                    ${match.details ? `
                    <div class="match-info-item full-width">
                        <div class="info-label">比赛详情</div>
                        <div class="info-value">${match.details}</div>
                    </div>
                    ` : ''}
                </div>

                <!-- HTML5 多媒体集锦区域 -->
                <div class="match-highlights">
                    <h4><i class="fas fa-video"></i> 比赛集锦</h4>
                    <div class="video-container">
                        <video id="match-video" controls preload="metadata" poster="images/video-poster.jpg" class="match-video">
                            <source src="videos/match-highlights.mp4" type="video/mp4">
                            <source src="videos/match-highlights.webm" type="video/webm">
                            <track kind="subtitles" src="videos/subtitles-zh.vtt" srclang="zh" label="中文字幕" default>
                            <track kind="captions" src="videos/captions-zh.vtt" srclang="zh" label="中文说明">
                            <p class="video-fallback">
                                您的浏览器不支持HTML5视频播放。
                                <a href="videos/match-highlights.mp4" download>点击下载视频</a>
                            </p>
                        </video>
                        <div class="video-controls">
                            <button id="play-pause-btn" class="video-btn" title="播放/暂停">
                                <i class="fas fa-play"></i>
                            </button>
                            <button id="mute-btn" class="video-btn" title="静音/取消静音">
                                <i class="fas fa-volume-up"></i>
                            </button>
                            <button id="fullscreen-btn" class="video-btn" title="全屏">
                                <i class="fas fa-expand"></i>
                            </button>
                            <div class="video-progress">
                                <div class="progress-bar">
                                    <div id="progress-fill" class="progress-fill"></div>
                                </div>
                                <span id="video-time" class="video-time">00:00 / 00:00</span>
                            </div>
                        </div>
                    </div>
                    <div class="video-info">
                        <p><i class="fas fa-info-circle"></i> 精彩瞬间回放，展示比赛关键时刻</p>
                        <div class="video-stats">
                            <span class="stat-item"><i class="fas fa-clock"></i> 时长: <span id="video-duration">--:--</span></span>
                            <span class="stat-item"><i class="fas fa-eye"></i> 观看次数: <span id="view-count">0</span></span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 显示详情容器
        detailContainer.style.display = 'block';

        // 初始化视频控制功能
        initVideoControls();

    } catch (error) {
        console.error("显示比赛详情失败:", error);
        document.getElementById('match-detail').innerHTML =
            `<p class="no-data">加载详情失败: ${error.message}</p>`;
    }
}

/**
 * 管理页面初始化
 */
function initManagePage () {
    // 初始化标签页功能
    initTabs();

    // 初始化各个管理模块
    initTournamentManagement();
    initTeamManagement();
    initPlayerManagement();
    initMatchManagement();
    initBackupManagement();
}

/**
 * 初始化标签页功能
 */
function initTabs () {
    const tabs = document.querySelectorAll('.tab');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabs.forEach(tab => {
        tab.addEventListener('click', function () {
            // 移除所有标签页的活动状态
            tabs.forEach(t => t.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));

            // 设置当前标签页为活动状态
            this.classList.add('active');
            const tabId = `${this.dataset.tab}-tab`;
            document.getElementById(tabId).classList.add('active');
        });
    });
}

/**
 * 初始化赛事管理
 */
function initTournamentManagement () {
    // 在这里实现赛事管理功能
}

/**
 * 初始化球队管理
 */
function initTeamManagement () {
    // 在这里实现球队管理功能
}

/**
 * 初始化球员管理
 */
function initPlayerManagement () {
    // 在这里实现球员管理功能
}

/**
 * 初始化比赛管理
 */
function initMatchManagement () {
    // 在这里实现比赛管理功能
}

/**
 * 初始化数据备份管理
 */
function initBackupManagement () {
    // 在这里实现数据备份与恢复功能
}

/**
 * 格式化日期为YYYY-MM-DD格式
 */
function formatDate (date) {
    // 检查日期参数是否有效
    if (!date) return '未知日期';

    try {
        // 尝试将字符串转换为日期对象
        let dateObj;
        if (typeof date === 'string') {
            // 如果是ISO格式的日期字符串，直接使用
            if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
                return date; // 已经是YYYY-MM-DD格式，直接返回
            }
            dateObj = new Date(date);
        } else if (date instanceof Date) {
            dateObj = date;
        } else {
            return '无效日期';
        }

        // 检查是否为有效日期
        if (isNaN(dateObj.getTime())) {
            return '无效日期';
        }

        // 格式化为YYYY-MM-DD
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const day = String(dateObj.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
    } catch (error) {
        console.error('日期格式化错误:', error, '原始值:', date);
        return '日期错误';
    }
}

/**
 * 格式化时间为 HH:MM 格式
 */
function formatTime (date) {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
}

/**
 * 初始化赛事搜索功能
 */
function initTournamentSearch () {
    console.log("初始化赛事搜索功能");
    const searchInput = document.getElementById('tournament-search');
    const searchBtn = document.getElementById('search-btn');

    if (searchInput && searchBtn) {
        // 点击搜索按钮时执行搜索
        searchBtn.addEventListener('click', function () {
            const searchTerm = searchInput.value.trim();
            const filterValue = document.getElementById('tournament-filter').value;
            loadTournaments(filterValue, searchTerm);
        });

        // 按下回车键时执行搜索
        searchInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                const searchTerm = searchInput.value.trim();
                const filterValue = document.getElementById('tournament-filter').value;
                loadTournaments(filterValue, searchTerm);
            }
        });
    }
}

/**
 * 初始化赛事筛选功能
 */
function initTournamentFilter () {
    console.log("初始化赛事筛选功能");
    const filterSelect = document.getElementById('tournament-filter');

    if (filterSelect) {
        filterSelect.addEventListener('change', function () {
            const searchTerm = document.getElementById('tournament-search').value.trim();
            loadTournaments(this.value, searchTerm);
        });
    }
}

/**
 * 初始化HTML5视频控制功能
 */
function initVideoControls () {
    console.log('🎥 初始化视频控制功能...');

    const video = document.getElementById('match-video');
    const playPauseBtn = document.getElementById('play-pause-btn');
    const muteBtn = document.getElementById('mute-btn');
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    const progressFill = document.getElementById('progress-fill');
    const videoTime = document.getElementById('video-time');
    const videoDuration = document.getElementById('video-duration');
    const viewCount = document.getElementById('view-count');

    if (!video) {
        console.log('视频元素未找到');
        return;
    }

    // 视频加载完成后的处理
    video.addEventListener('loadedmetadata', () => {
        const duration = formatVideoTime(video.duration);
        if (videoDuration) {
            videoDuration.textContent = duration;
        }
        console.log('视频元数据加载完成，时长:', duration);
    });

    // 播放/暂停控制
    if (playPauseBtn) {
        playPauseBtn.addEventListener('click', () => {
            if (video.paused) {
                video.play();
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                incrementViewCount();
            } else {
                video.pause();
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            }
        });
    }

    // 静音控制
    if (muteBtn) {
        muteBtn.addEventListener('click', () => {
            video.muted = !video.muted;
            muteBtn.innerHTML = video.muted ?
                '<i class="fas fa-volume-mute"></i>' :
                '<i class="fas fa-volume-up"></i>';
        });
    }

    // 全屏控制
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', () => {
            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        });
    }

    // 进度条更新
    video.addEventListener('timeupdate', () => {
        if (video.duration) {
            const progress = (video.currentTime / video.duration) * 100;
            if (progressFill) {
                progressFill.style.width = progress + '%';
            }

            if (videoTime) {
                const current = formatVideoTime(video.currentTime);
                const total = formatVideoTime(video.duration);
                videoTime.textContent = `${current} / ${total}`;
            }
        }
    });

    // 进度条点击跳转
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        progressBar.addEventListener('click', (e) => {
            const rect = progressBar.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const width = rect.width;
            const clickTime = (clickX / width) * video.duration;
            video.currentTime = clickTime;
        });
    }

    // 视频结束处理
    video.addEventListener('ended', () => {
        if (playPauseBtn) {
            playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
        }
        if (progressFill) {
            progressFill.style.width = '100%';
        }
    });

    // 视频错误处理
    video.addEventListener('error', (e) => {
        console.error('视频加载错误:', e);
        const videoContainer = document.querySelector('.video-container');
        if (videoContainer) {
            videoContainer.innerHTML = `
                <div class="video-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>视频加载失败，请稍后重试</p>
                    <button onclick="location.reload()" class="btn btn-primary">
                        <i class="fas fa-refresh"></i> 刷新页面
                    </button>
                </div>
            `;
        }
    });

    // 初始化观看次数
    const savedViewCount = localStorage.getItem('match-video-views') || '0';
    if (viewCount) {
        viewCount.textContent = savedViewCount;
    }
}

/**
 * 格式化视频时间为 MM:SS 格式
 */
function formatVideoTime (seconds) {
    if (isNaN(seconds)) return '00:00';

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * 增加观看次数
 */
function incrementViewCount () {
    const currentCount = parseInt(localStorage.getItem('match-video-views') || '0');
    const newCount = currentCount + 1;
    localStorage.setItem('match-video-views', newCount.toString());

    const viewCountElement = document.getElementById('view-count');
    if (viewCountElement) {
        viewCountElement.textContent = newCount.toString();
    }
}