<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>球员 - 中国青少年足球数据平台</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>

<body>
    <header>
        <div class="logo">
            <img src="images/logo.png" alt="足球数据平台" id="logo-img">
            <h1>青少年足球数据</h1>
        </div>
        <div class="header-actions">
            <a href="#" id="search-toggle"><i class="fas fa-search"></i></a>
        </div>
    </header>

    <!-- 顶部导航菜单 -->
    <nav class="top-nav">
        <a href="index.html">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="tournaments.html">
            <i class="fas fa-trophy"></i>
            <span>赛事</span>
        </a>
        <a href="matches.html">
            <i class="fas fa-futbol"></i>
            <span>比赛</span>
        </a>
        <a href="teams.html">
            <i class="fas fa-users"></i>
            <span>球队</span>
        </a>
        <a href="manage.html">
            <i class="fas fa-cog"></i>
            <span>管理</span>
        </a>
    </nav>

    <main>
        <section class="page-header">
            <h2><i class="fas fa-user"></i> 球员信息</h2>
        </section>

        <div class="search-bar">
            <input type="text" id="player-search" placeholder="搜索球员...">
            <button id="search-btn"><i class="fas fa-search"></i></button>
        </div>

        <section class="filter-section">
            <div class="filter-container">
                <label for="player-filter">筛选：</label>
                <select id="player-filter">
                    <option value="all">全部球员</option>
                    <option value="u12">U12</option>
                    <option value="u15">U15</option>
                    <option value="u17">U17</option>
                    <option value="u20">U20</option>
                </select>
            </div>
        </section>

        <section class="players-list">
            <div class="players-container" id="players-container">
                <!-- 动态加载球员信息 -->
            </div>
        </section>

        <section class="player-detail" id="player-detail">
            <!-- 点击球员后显示详细信息 -->
        </section>
    </main>

    <!-- 底部导航栏 -->
    <nav class="bottom-nav">
        <a href="index.html">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="tournaments.html">
            <i class="fas fa-trophy"></i>
            <span>赛事</span>
        </a>
        <a href="matches.html">
            <i class="fas fa-futbol"></i>
            <span>比赛</span>
        </a>
        <a href="teams.html">
            <i class="fas fa-users"></i>
            <span>球队</span>
        </a>
        <a href="manage.html">
            <i class="fas fa-cog"></i>
            <span>管理</span>
        </a>
    </nav>

    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>
</body>

</html>