<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>球员管理 - 中国青少年足球数据平台</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- 统一导航栏由Navigation组件填充 -->
    <header></header>

    <main>
        <section class="page-header">
            <h2><i class="fas fa-user"></i> 球员信息</h2>
        </section>

        <!-- 搜索栏 -->
        <div id="search-container"></div>

        <section class="filter-section">
            <div class="filter-container">
                <label for="player-filter">筛选：</label>
                <select id="player-filter">
                    <option value="all">全部球员</option>
                    <option value="u12">U12</option>
                    <option value="u15">U15</option>
                    <option value="u17">U17</option>
                    <option value="u20">U20</option>
                </select>
            </div>
        </section>

        <section class="players-list">
            <div class="players-container" id="players-container">
                <!-- 动态加载球员信息 -->
            </div>
        </section>

        <section class="player-detail" id="player-detail">
            <!-- 点击球员后显示详细信息 -->
        </section>
    </main>

    <!-- 统一底部导航由Navigation组件填充 -->
    <footer></footer>

    <!-- 组件 -->
    <script src="components/Navigation.js"></script>
    <script src="components/SearchBar.js"></script>
    <script src="components/Card.js"></script>
    <!-- 数据和应用逻辑 -->
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 初始化组件
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🚀 初始化球员页面...');

            // 初始化统一导航
            Navigation.init();

            // 初始化搜索栏
            document.getElementById('search-container').innerHTML = SearchBar.render('player-search', '搜索球员...');
            SearchBar.init('player-search', searchPlayers);

            // 初始化球员筛选
            initPlayerFilter();

            // 加载球员列表
            loadPlayers();
        });

        // 搜索球员函数
        function searchPlayers (query) {
            console.log('搜索球员:', query);
            // 这里添加搜索逻辑
        }

        // 初始化球员筛选
        function initPlayerFilter () {
            const filterSelect = document.getElementById('player-filter');
            if (filterSelect) {
                filterSelect.addEventListener('change', function () {
                    loadPlayers(this.value);
                });
            }
        }

        // 加载球员列表
        function loadPlayers (filter = 'all') {
            console.log('加载球员列表, 筛选:', filter);
            // 这里添加加载球员的逻辑
            const container = document.getElementById('players-container');
            if (container) {
                container.innerHTML = '<p class="no-data">球员功能正在开发中...</p>';
            }
        }
    </script>
</body>

</html>