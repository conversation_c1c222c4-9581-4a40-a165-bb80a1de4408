/**
 * 中国青少年足球数据平台 - 数据初始化模块
 * 
 * 该模块用于初始化系统中的示例数据，包括：
 * - 赛事信息
 * - 球队信息
 * - 球员信息
 * - 比赛信息
 */

// 初始化函数
function initializeData () {
    console.log("初始化示例数据...");

    // 检查是否已经初始化过
    if (localStorage.getItem('data_initialized')) {
        console.log("数据已初始化，跳过");
        return;
    }

    // 初始化球队数据
    initializeTeams();

    // 初始化赛事数据
    initializeTournaments();

    // 初始化球员数据
    initializePlayers();

    // 初始化比赛数据
    initializeMatches();

    // 标记为已初始化
    localStorage.setItem('data_initialized', 'true');
    console.log("数据初始化完成");
}

// 初始化球队数据
function initializeTeams () {
    console.log("初始化球队数据...");

    const teams = [
        {
            id: "team-001",
            name: "广州恒大青训",
            category: "u17",
            region: "广东",
            foundYear: "2011",
            description: "广州恒大足球俱乐部青训梯队，中国最早系统化建立的职业俱乐部青训体系之一",
            logo: "https://img0.baidu.com/it/u=3353261045,3935407969&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
            coach: "李明",
            homeStadium: "恒大足球学校训练基地",
            contactInfo: "<EMAIL>"
        },
        {
            id: "team-002",
            name: "上海申花青年队",
            category: "u15",
            region: "上海",
            foundYear: "2000",
            description: "上海申花足球俱乐部青年队，拥有完善的青训体系，培养了众多优秀球员",
            logo: "https://img1.baidu.com/it/u=3221950917,2872849564&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
            coach: "张伟",
            homeStadium: "申花青训基地",
            contactInfo: "<EMAIL>"
        },
        {
            id: "team-003",
            name: "北京国安U17",
            category: "u17",
            region: "北京",
            foundYear: "2005",
            description: "北京国安足球俱乐部U17梯队，专注培养青少年足球人才",
            logo: "images/logo.png",
            coach: "王强",
            homeStadium: "国安青训基地",
            contactInfo: "<EMAIL>"
        },
        {
            id: "team-004",
            name: "山东鲁能U12",
            category: "u12",
            region: "山东",
            foundYear: "2008",
            description: "山东鲁能足球俱乐部U12梯队，青训成果显著",
            logo: "images/logo.png",
            coach: "刘军",
            homeStadium: "鲁能泰山训练基地",
            contactInfo: "<EMAIL>"
        },
        {
            id: "team-005",
            name: "重庆两江竞技青年队",
            category: "u20",
            region: "重庆",
            foundYear: "2018",
            description: "重庆两江竞技足球俱乐部青年队，致力于培养西南地区足球人才",
            logo: "images/logo.png",
            coach: "陈勇",
            homeStadium: "两江足球学院",
            contactInfo: "<EMAIL>"
        },
        {
            id: "team-006",
            name: "大连人青训",
            category: "u15",
            region: "辽宁",
            foundYear: "2009",
            description: "大连人足球俱乐部青训梯队，拥有悠久的青训历史",
            logo: "images/logo.png",
            coach: "赵刚",
            homeStadium: "大连足球青训中心",
            contactInfo: "<EMAIL>"
        },
        {
            id: "team-007",
            name: "河南嵩山龙门U17",
            category: "u17",
            region: "河南",
            foundYear: "2015",
            description: "河南嵩山龙门足球俱乐部U17梯队，中原地区有影响力的青训基地",
            logo: "images/logo.png",
            coach: "黄河",
            homeStadium: "嵩山足球基地",
            contactInfo: "<EMAIL>"
        },
        {
            id: "team-008",
            name: "武汉三镇青年队",
            category: "u20",
            region: "湖北",
            foundYear: "2017",
            description: "武汉三镇足球俱乐部青年队，注重青少年球员的全面发展",
            logo: "images/logo.png",
            coach: "张楚",
            homeStadium: "武汉足球训练中心",
            contactInfo: "<EMAIL>"
        },
        {
            id: "team-009",
            name: "天津津门虎U12",
            category: "u12",
            region: "天津",
            foundYear: "2013",
            description: "天津津门虎足球俱乐部U12梯队，小球员技术基础扎实",
            logo: "images/logo.png",
            coach: "李虎",
            homeStadium: "津门青少年足球基地",
            contactInfo: "<EMAIL>"
        },
        {
            id: "team-010",
            name: "深圳队青训",
            category: "u15",
            region: "广东",
            foundYear: "2014",
            description: "深圳足球队青训梯队，南方足球新兴力量",
            logo: "images/logo.png",
            coach: "陈深",
            homeStadium: "深圳足球学院",
            contactInfo: "<EMAIL>"
        }
    ];

    // 保存到localStorage
    localStorage.setItem(STORAGE_KEYS.TEAMS, JSON.stringify(teams));
    console.log(`已初始化 ${teams.length} 支球队`);
}

// 初始化赛事数据
function initializeTournaments () {
    console.log("初始化赛事数据...");

    const tournaments = [
        {
            id: "tournament-001",
            name: "全国青少年足球U17锦标赛",
            category: "u17",
            startDate: "2023-08-10",
            endDate: "2023-08-20",
            location: "广州",
            description: "全国最高水平的U17青少年足球赛事，展示中国足球未来之星",
            organizer: "中国足协青少年部",
            status: "completed"
        },
        {
            id: "tournament-002",
            name: "全国青少年足球联赛(U15)",
            category: "u15",
            startDate: "2023-09-15",
            endDate: "2023-10-30",
            location: "多地联办",
            description: "面向全国U15年龄段的大型联赛，促进各地区青少年足球交流",
            organizer: "中国足协",
            status: "completed"
        },
        {
            id: "tournament-003",
            name: "希望杯U12足球邀请赛",
            category: "u12",
            startDate: "2024-04-15",
            endDate: "2024-04-25",
            location: "上海",
            description: "为低龄段球员提供的高水平比赛平台，注重球员的技术培养与比赛体验",
            organizer: "上海市足协",
            status: "upcoming"
        },
        {
            id: "tournament-004",
            name: "全国青年足球超级联赛",
            category: "u20",
            startDate: "2024-05-10",
            endDate: "2024-07-30",
            location: "全国分区",
            description: "面向U20球员的高水平联赛，是球员进入职业队前的重要赛事",
            organizer: "中国足协青少年部",
            status: "upcoming"
        },
        {
            id: "tournament-005",
            name: "城市之星青少年足球赛",
            category: "u15",
            startDate: "2024-03-05",
            endDate: "2024-03-15",
            location: "北京",
            description: "汇集全国重点城市的U15梯队，展示各地区青训成果",
            organizer: "北京市足协",
            status: "completed"
        }
    ];

    // 保存到localStorage
    localStorage.setItem(STORAGE_KEYS.TOURNAMENTS, JSON.stringify(tournaments));
    console.log(`已初始化 ${tournaments.length} 个赛事`);
}

// 初始化球员数据
function initializePlayers () {
    console.log("初始化球员数据...");

    // 这里可以添加一些球员数据
    const players = [];

    // 保存到localStorage
    localStorage.setItem(STORAGE_KEYS.PLAYERS, JSON.stringify(players));
    console.log(`已初始化 ${players.length} 名球员`);
}

// 初始化比赛数据
function initializeMatches () {
    console.log("初始化比赛数据...");

    const matches = [
        {
            id: "match-001",
            tournamentId: "tournament-001",
            homeTeamId: "team-001",
            awayTeamId: "team-003",
            date: "2023-08-12",
            time: "15:00",
            venue: "广州天河体育中心",
            status: "completed",
            homeScore: 2,
            awayScore: 1,
            details: "精彩的比赛，广州恒大青训在下半场凭借优势取胜"
        },
        {
            id: "match-002",
            tournamentId: "tournament-001",
            homeTeamId: "team-003",
            awayTeamId: "team-007",
            date: "2023-08-14",
            time: "16:30",
            venue: "广州大学城体育场",
            status: "completed",
            homeScore: 0,
            awayScore: 0,
            details: "双方实力相当，最终战成平局"
        },
        {
            id: "match-003",
            tournamentId: "tournament-002",
            homeTeamId: "team-002",
            awayTeamId: "team-006",
            date: "2023-09-20",
            time: "14:00",
            venue: "上海嘉定体育场",
            status: "completed",
            homeScore: 3,
            awayScore: 1,
            details: "上海申花青年队展现出色的进攻能力"
        },
        {
            id: "match-004",
            tournamentId: "tournament-003",
            homeTeamId: "team-004",
            awayTeamId: "team-009",
            date: "2024-04-17",
            time: "10:30",
            venue: "上海市青少年足球训练基地",
            status: "upcoming",
            homeScore: null,
            awayScore: null,
            details: "希望杯小组赛重要对决"
        },
        {
            id: "match-005",
            tournamentId: "tournament-004",
            homeTeamId: "team-005",
            awayTeamId: "team-008",
            date: "2024-05-15",
            time: "19:30",
            venue: "重庆奥体中心",
            status: "upcoming",
            homeScore: null,
            awayScore: null,
            details: "超级联赛首轮较量"
        },
        {
            id: "match-006",
            tournamentId: "tournament-005",
            homeTeamId: "team-010",
            awayTeamId: "team-002",
            date: "2024-03-10",
            time: "15:00",
            venue: "北京工人体育场",
            status: "completed",
            homeScore: 1,
            awayScore: 2,
            details: "紧张激烈的比赛，上海申花青年队在最后时刻绝杀取胜"
        }
    ];

    // 保存到localStorage
    localStorage.setItem(STORAGE_KEYS.MATCHES, JSON.stringify(matches));
    console.log(`已初始化 ${matches.length} 场比赛`);
}

// DOM加载完成后初始化数据
document.addEventListener('DOMContentLoaded', function () {
    // 只有在尚未初始化数据时才执行初始化
    if (!localStorage.getItem('data_initialized')) {
        initializeData();
    }
}); 