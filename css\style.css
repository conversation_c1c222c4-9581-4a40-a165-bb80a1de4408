/* 全局样式 */
:root {
    --primary-color: #2ecc71;
    /* 绿色主题色 */
    --secondary-color: #27ae60;
    --accent-color: #e74c3c;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --gray-color: #dadce0;
    --text-color: #3c4043;
    --header-height: 50px;
    --bottom-nav-height: 55px;
    --top-nav-height: 50px;
    /* 卡片变量 */
    --card-bg: white;
    --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-color: #eaeaea;
    --text-primary: #333;
    --text-secondary: #666;
    --bg-secondary: #f5f5f5;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Roboto', 'Noto Sans SC', Arial, sans-serif;
}

body {
    color: var(--text-color);
    background-color: #f2f2f2;
    line-height: 1.6;
    padding-bottom: var(--bottom-nav-height);
}

/* 通用元素样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s;
}

a:hover {
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

/* 布局容器 */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 统一头部样式 - HTML5语义化设计 */
header {
    background: linear-gradient(135deg, white, #f8f9fa);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    height: var(--header-height);
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 35px;
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--primary-color);
}

/* 统一底部导航样式 */
.bottom-nav {
    background: linear-gradient(135deg, white, #f8f9fa);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    height: var(--bottom-nav-height);
    display: flex !important;
    justify-content: space-around;
    align-items: center;
    border-top: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.bottom-nav .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    padding: 0.5rem 0.25rem;
    width: 20%;
    transition: all 0.3s ease;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.bottom-nav .nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.bottom-nav .nav-item:hover::before,
.bottom-nav .nav-item.active::before {
    opacity: 0.1;
}

.bottom-nav .nav-item:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.bottom-nav .nav-icon {
    position: relative;
    margin-bottom: 0.25rem;
}

.bottom-nav .nav-icon i {
    font-size: 1.3rem;
    transition: all 0.3s ease;
}

.bottom-nav .nav-item:hover .nav-icon i {
    transform: scale(1.1);
}

.bottom-nav .nav-label {
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.bottom-nav .nav-item.active {
    color: var(--primary-color);
    position: relative;
}

.bottom-nav .nav-item.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 3px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 8px var(--primary-color);
}

.bottom-nav .nav-item.active .nav-icon i {
    transform: scale(1.1);
}

.bottom-nav .nav-item.active .nav-label {
    font-weight: 600;
}

/* 旧导航样式隐藏 */
#main-nav {
    display: none;
}

.menu-toggle {
    display: none;
}

/* 主内容区 - 适应统一导航 */
main {
    margin-top: var(--header-height);
    padding: 1rem;
    min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height));
    padding-bottom: calc(var(--bottom-nav-height) + 1rem);
    background: #f8f9fa;
}

/* 英雄区 */
.hero {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 30px 15px;
    text-align: center;
    border-radius: 8px;
    margin-bottom: 20px;
}

.hero h2 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

/* 数据卡片 */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-card i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    color: var(--primary-color);
}

.stat-card h3 {
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.stat-card p {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--dark-color);
}

/* 最近内容区域 */
.recent-section {
    margin-bottom: 25px;
}

.recent-section h2 {
    font-size: 1.3rem;
    margin-bottom: 12px;
    color: var(--dark-color);
    border-left: 4px solid var(--primary-color);
    padding-left: 10px;
}

.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
}

/* 内容卡片 */
.card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
}

.card-header h3 {
    font-size: 1.1rem;
    margin: 0;
}

.card-body {
    padding: 12px;
}

.card-footer {
    padding: 8px 12px;
    background-color: var(--light-color);
    border-top: 1px solid var(--gray-color);
    display: flex;
    justify-content: space-between;
}

/* 比赛卡片特殊样式 */
.match-teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 5px 0;
}

.match-teams .team {
    flex: 1;
    text-align: center;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 5px;
}

.match-teams .home-team {
    text-align: right;
}

.match-teams .away-team {
    text-align: left;
}

.match-score,
.match-time {
    padding: 5px 10px;
    background-color: rgba(46, 204, 113, 0.1);
    border-radius: 4px;
    font-weight: bold;
    text-align: center;
    min-width: 60px;
    margin: 0 8px;
}

.match-score {
    color: var(--primary-color);
}

.match-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #666;
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-color);
}

.page-header h2 {
    display: flex;
    align-items: center;
    font-size: 1.3rem;
    color: var(--dark-color);
}

.page-header h2 i {
    margin-right: 8px;
    color: var(--primary-color);
}

/* 搜索栏样式 */
.search-bar {
    display: flex;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
}

.search-bar.active {
    max-height: 50px;
    opacity: 1;
    margin-bottom: 15px;
}

.search-bar input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid var(--gray-color);
    border-radius: 4px 0 0 4px;
    font-size: 0.95rem;
}

.search-bar button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0 15px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.search-bar button:hover {
    background-color: var(--secondary-color);
}

/* 搜索和筛选 */
.search-container {
    display: flex;
    align-items: center;
}

.filter-section {
    margin-bottom: 20px;
}

.filter-container {
    display: flex;
    align-items: center;
}

.filter-container label {
    margin-right: 10px;
}

.filter-container select {
    padding: 8px 10px;
    border: 1px solid var(--gray-color);
    border-radius: 4px;
    background-color: white;
}

/* 列表样式 */
.tournament-list,
.teams-list,
.players-list,
.matches-list {
    margin-bottom: 30px;
}

.tournament-container,
.teams-container,
.players-container,
.matches-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

/* 详情样式 */
.tournament-detail,
.team-detail,
.player-detail,
.match-detail {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 管理页面样式 */
.tabs-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tabs {
    display: flex;
    background-color: var(--primary-color);
    color: white;
}

.tab {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.tab:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.tab.active {
    background-color: white;
    color: var(--primary-color);
    font-weight: 500;
}

.tab-content {
    padding: 20px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 表单样式 */
.form-container {
    background-color: var(--light-color);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.form-container h4 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--gray-color);
    border-radius: 4px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.form-actions button {
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.form-actions button[type="submit"] {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.form-actions .cancel-btn {
    background-color: var(--light-color);
    border: 1px solid var(--gray-color);
    color: var(--text-color);
}

/* 按钮样式 */
.add-btn {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.add-btn i {
    margin-right: 5px;
}

.action-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
}

.action-btn i {
    margin-right: 5px;
}

.action-btn.danger {
    background-color: var(--accent-color);
}

/* 管理列表样式 */
.manage-list {
    margin-top: 20px;
}

.manage-item {
    background-color: white;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.manage-item-info {
    flex: 1;
}

.manage-item-actions {
    display: flex;
    gap: 10px;
}

.edit-btn,
.delete-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.edit-btn {
    color: var(--primary-color);
}

.delete-btn {
    color: var(--accent-color);
}

/* 日历样式 */
.calendar-container {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.calendar-header button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-day {
    aspect-ratio: 1 / 1;
    border: 1px solid var(--gray-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px;
    position: relative;
}

.calendar-day.has-match {
    background-color: rgba(52, 168, 83, 0.1);
}

.calendar-day.today {
    border-color: var(--primary-color);
    font-weight: bold;
}

.calendar-day .day-number {
    position: absolute;
    top: 5px;
    left: 5px;
    font-size: 0.8rem;
}

.calendar-day .match-count {
    margin-top: 15px;
    font-size: 0.7rem;
    color: var(--primary-color);
}

/* 备份区域样式 */
.backup-actions {
    margin-bottom: 20px;
}

.import-container {
    display: inline-block;
}

.backup-info {
    background-color: var(--light-color);
    padding: 15px;
    border-radius: 4px;
    margin-top: 20px;
}

.backup-info p {
    margin-bottom: 10px;
}

/* 响应式设计 - 统一导航适配 */
@media (max-width: 768px) {

    /* 顶部导航移动端适配 */
    .brand-text {
        display: none;
    }

    .brand-logo {
        width: 28px;
        height: 28px;
    }

    .nav-action-btn {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }

    .user-menu {
        right: 0.5rem;
        min-width: 180px;
    }

    /* 底部导航移动端适配 */
    .bottom-nav .nav-label {
        font-size: 0.7rem;
    }

    .bottom-nav .nav-icon i {
        font-size: 1.2rem;
    }

    /* 主内容区适配 */
    main {
        padding: 0.75rem;
    }

    .hero {
        padding: 20px 15px;
    }

    .hero h2 {
        font-size: 1.3rem;
    }

    .hero p {
        font-size: 0.9rem;
    }

    .card-container {
        grid-template-columns: 1fr;
    }

    /* 移动端视频播放器适配 */
    .match-highlights {
        padding: 1rem;
        margin-top: 1rem;
    }

    .video-controls {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .video-btn {
        width: 36px;
        height: 36px;
    }

    .video-time {
        font-size: 0.8rem;
        min-width: 70px;
    }

    .video-stats {
        flex-direction: column;
        gap: 0.75rem;
    }

    .stat-item {
        font-size: 0.85rem;
    }

    /* 移动端比赛对阵布局优化 */
    .teams-vs-container {
        padding: 1rem 0.75rem;
        margin-bottom: 1rem;
    }

    .team-side .team-logo {
        width: 50px;
        height: 50px;
        margin-bottom: 0.5rem;
    }

    .team-side .team-name {
        font-size: 0.9rem;
    }

    .team-side .team-category {
        font-size: 0.75rem;
        padding: 0.15rem 0.4rem;
    }

    .match-center {
        min-width: 70px;
    }

    .match-center .match-score {
        font-size: 1.3rem;
        padding: 0.6rem 0.8rem;
    }

    .match-center .match-time {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    /* 移动端轮播图优化 */
    .carousel-container {
        height: 250px;
    }

    .slide-content {
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
        text-align: center;
    }

    .slide-image {
        height: 120px;
    }

    .slide-text h3 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .slide-text p {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .slide-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }

    .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .carousel-prev {
        left: 0.5rem;
    }

    .carousel-next {
        right: 0.5rem;
    }

    .indicator {
        width: 10px;
        height: 10px;
    }

    #main-nav {
        position: fixed;
        top: var(--header-height);
        left: -100%;
        width: 70%;
        height: calc(100vh - var(--header-height));
        background-color: white;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        transition: left 0.3s;
        z-index: 1000;
    }

    #main-nav.active {
        left: 0;
    }

    #main-nav ul {
        flex-direction: column;
        padding: 20px;
    }

    #main-nav ul li {
        margin: 10px 0;
    }

    .menu-toggle {
        display: block;
    }

    .quick-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .search-container {
        width: 100%;
        margin-top: 10px;
    }

    .search-container input {
        width: 100%;
    }

    .tournament-container,
    .teams-container,
    .players-container,
    .matches-container {
        grid-template-columns: 1fr;
    }

    .tabs {
        flex-wrap: wrap;
    }

    .tab {
        padding: 10px;
        font-size: 0.9rem;
    }
}

@media (min-width: 768px) {
    .quick-stats {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 480px) {
    .quick-stats {
        grid-template-columns: 1fr;
    }

    .calendar-grid {
        grid-template-columns: repeat(7, 1fr);
        gap: 2px;
    }

    .calendar-day {
        padding: 2px;
    }

    .calendar-day .day-number {
        font-size: 0.7rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions button {
        width: 100%;
        margin-bottom: 5px;
    }
}

/* 添加搜索按钮样式 */
.header-actions {
    display: flex;
    align-items: center;
}

.header-actions a {
    color: var(--text-color);
    font-size: 1.2rem;
    padding: 8px;
}

/* 查看更多链接 */
.section-more {
    text-align: right;
    margin-top: 8px;
    font-size: 0.9rem;
}

.section-more a {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
}

.section-more a i {
    margin-left: 5px;
    transition: transform 0.3s;
}

.section-more a:hover i {
    transform: translateX(3px);
}

/* 无数据提示 */
.no-data {
    text-align: center;
    padding: 30px;
    background-color: #f8f8f8;
    border-radius: var(--border-radius);
    color: #888;
    font-style: italic;
    margin: 20px 0;
    border: 1px dashed var(--border-color);
}

/* 详情页面返回按钮 */
.back-btn {
    background-color: var(--light-color);
    border: 1px solid var(--gray-color);
    color: var(--text-color);
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    margin-top: 20px;
}

.back-btn i {
    margin-right: 5px;
}

/* 详情信息样式 */
.detail-info {
    background-color: var(--light-color);
    padding: 15px;
    border-radius: 4px;
    margin: 15px 0;
}

.detail-info p {
    margin-bottom: 8px;
}

.detail-info p:last-child {
    margin-bottom: 0;
}

/* 统一顶部导航栏样式 */
.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
    height: 100%;
    position: relative;
}

/* 品牌区域 */
.nav-brand {
    flex: 1;
}

.brand-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.brand-link:hover {
    color: var(--primary-color);
    transform: translateY(-1px);
}

.brand-logo {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.brand-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* 导航操作按钮 */
.nav-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.nav-action-btn {
    background: none;
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.nav-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(46, 204, 113, 0.3);
}

/* 用户菜单下拉 */
.user-menu {
    position: absolute;
    top: 100%;
    right: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-color);
    min-width: 200px;
    z-index: 1001;
    overflow: hidden;
}

.user-info {
    padding: 1rem;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-primary);
}

.user-info i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.menu-divider {
    height: 1px;
    background: var(--border-color);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.menu-item:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

.menu-item i {
    width: 16px;
    text-align: center;
}

/* 登录页面样式 */
.login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - var(--header-height) - 60px);
    background-color: #f5f5f5;
}

.login-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    width: 90%;
    max-width: 450px;
}

.login-container h2 {
    color: var(--primary-color);
    margin-bottom: 25px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--gray-color);
    border-radius: 4px;
    font-size: 16px;
}

.form-group.remember-me {
    display: flex;
    align-items: center;
}

.form-group.remember-me input {
    width: auto;
    margin-right: 8px;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.primary-btn,
.secondary-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 16px;
    transition: background-color 0.2s;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    flex: 1;
    margin-right: 10px;
}

.primary-btn:hover {
    background-color: var(--secondary-color);
}

.secondary-btn {
    background-color: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--gray-color);
    flex: 1;
}

.secondary-btn:hover {
    background-color: #e9e9e9;
}

.login-error,
.register-error {
    color: var(--accent-color);
    font-size: 14px;
    margin-top: 10px;
    text-align: center;
    min-height: 20px;
}

/* 用户信息显示 */
.user-info {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.user-info .user-name {
    margin-right: 10px;
    font-weight: 500;
}

.user-info .logout-btn {
    color: var(--text-color);
    font-size: 0.9rem;
    cursor: pointer;
}

.user-info .logout-btn:hover {
    color: var(--accent-color);
}

/* 比赛详情页样式 */
.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.back-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.back-btn:hover {
    background: var(--primary-dark);
}

.status {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 13px;
    font-weight: 500;
}

.status.completed {
    background-color: #e0e0e0;
    color: #333;
}

.status.live {
    background-color: #f44336;
    color: white;
    animation: pulse 1.5s infinite;
}

.status.upcoming {
    background-color: #2196f3;
    color: white;
}

.detail-tournament {
    text-align: center;
    margin-bottom: 30px;
}

.detail-tournament h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.match-date-time {
    color: #666;
    margin-bottom: 5px;
}

.match-venue {
    color: #666;
    font-style: italic;
}

.match-detail-content {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.teams-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.team-detail {
    text-align: center;
    width: 40%;
}

.team-logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-bottom: 10px;
}

.team-name {
    font-size: 1.2rem;
    margin: 5px 0;
}

.team-category {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.match-score-large {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: bold;
}

.match-score-large.live {
    color: #f44336;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }
}

.score-divider {
    margin: 0 10px;
    color: #666;
}

.match-time-large {
    font-size: 1.5rem;
    color: #2196f3;
}

.match-info-container {
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.match-info-container h4 {
    margin-bottom: 15px;
    color: #444;
}

.match-info-item {
    display: flex;
    margin-bottom: 12px;
}

.info-label {
    width: 100px;
    font-weight: 600;
    color: #555;
}

.info-value {
    flex: 1;
}

.match-info-item.full-width {
    flex-direction: column;
}

.match-info-item.full-width .info-label {
    margin-bottom: 5px;
}

.match-info-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.match-info-item a:hover {
    text-decoration: underline;
}

/* 球队卡片样式 */
.team-card {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.team-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.team-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: var(--primary-color);
    color: white;
}

.team-card .card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.category-badge {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.team-card .card-body {
    padding: 15px;
    display: flex;
    gap: 15px;
}

.team-logo {
    flex: 0 0 80px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: #f5f5f5;
    border-radius: 5px;
}

.team-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.team-info {
    flex: 1;
}

.team-info p {
    margin: 5px 0;
    font-size: 14px;
    line-height: 1.4;
}

.team-card .card-footer {
    padding: 10px 15px;
    border-top: 1px solid #eee;
    text-align: right;
}

.view-btn {
    display: inline-block;
    padding: 6px 12px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s;
}

.view-btn:hover {
    background-color: var(--primary-dark);
}

/* 球队列表样式 */
.teams-list {
    margin-top: 15px;
}

.teams-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px 0;
}

@media (min-width: 768px) {
    .teams-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (min-width: 992px) {
    .teams-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    opacity: 0.9;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--gray-color);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #c0c2c5;
}

/* HTML5 视频播放器样式 */
.match-highlights {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1.5rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

.match-highlights h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
}

.video-container {
    position: relative;
    background: #000;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.match-video {
    width: 100%;
    height: auto;
    max-height: 400px;
    display: block;
    background: #000;
}

.video-controls {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-container:hover .video-controls {
    opacity: 1;
}

.video-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.video-btn:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

.video-progress {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 3px;
    transition: width 0.1s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.video-time {
    color: white;
    font-size: 0.85rem;
    font-weight: 500;
    min-width: 80px;
    text-align: center;
}

.video-info {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.video-info p {
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.video-stats {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.stat-item i {
    color: var(--primary-color);
}

.video-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
    color: var(--text-secondary);
}

.video-error i {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.video-error p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.video-fallback {
    padding: 2rem;
    text-align: center;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
}

.video-fallback a {
    color: var(--primary-color);
    text-decoration: underline;
}

/* 手机端优化的比赛对阵布局 */
.teams-vs-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, white, #f8f9fa);
    border-radius: var(--border-radius);
    padding: 1.5rem 1rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.teams-vs-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
            rgba(46, 204, 113, 0.05) 0%,
            transparent 25%,
            transparent 75%,
            rgba(39, 174, 96, 0.05) 100%);
    pointer-events: none;
}

.team-side {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 1;
}

.team-side .team-logo {
    width: 60px;
    height: 60px;
    background: white;
    border-radius: 50%;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--border-color);
    margin-bottom: 0.75rem;
    transition: transform 0.3s ease;
}

.team-side .team-logo:hover {
    transform: scale(1.05);
}

.team-side .team-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.team-info {
    width: 100%;
}

.team-side .team-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
}

.team-side .team-category {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0;
    background: var(--bg-secondary);
    padding: 0.2rem 0.5rem;
    border-radius: var(--border-radius-sm);
    display: inline-block;
}

.match-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    position: relative;
    z-index: 1;
}

.match-center .match-score {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
    box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
    margin-bottom: 0.5rem;
    min-width: 60px;
}

.match-center .match-time {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
    background: white;
    padding: 0.3rem 0.6rem;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
}

.match-center .vs-text {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0.5rem 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 比赛状态样式 */
.match-status {
    position: absolute;
    top: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    background: var(--accent-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.match-status.completed {
    background: var(--success-color, #27ae60);
}

.match-status.upcoming {
    background: var(--info-color, #3498db);
}

.match-status.live {
    background: var(--accent-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

/* HTML5轮播图样式 */
.hero-carousel {
    margin: 1.5rem 0;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    background: white;
}

.carousel-container {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.carousel-slides {
    position: relative;
    width: 100%;
    height: 100%;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.carousel-slide.active {
    opacity: 1;
}

.slide-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 2rem;
    gap: 2rem;
}

.slide-image {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.slide-text {
    flex: 1;
    color: white;
    text-align: left;
}

.slide-text h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.slide-text p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.slide-btn {
    background: white;
    color: var(--primary-color);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.slide-btn:hover {
    background: var(--bg-secondary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.slide-btn i {
    margin-right: 0.5rem;
}

/* 轮播控制按钮 */
.carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.carousel-btn:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.carousel-prev {
    left: 1rem;
}

.carousel-next {
    right: 1rem;
}

/* 轮播指示器 */
.carousel-indicators {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    z-index: 10;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: white;
    transform: scale(1.2);
}

.indicator:hover {
    background: rgba(255, 255, 255, 0.7);
}

/* 过滤器按钮组 */
.filter-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.filter-btn {
    padding: 8px 16px;
    background-color: #f0f0f0;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background-color: #e5e5e5;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}