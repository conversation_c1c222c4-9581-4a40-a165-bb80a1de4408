/**
 * 中国青少年足球数据平台 - 用户认证模块
 * 
 * 该模块用于处理网站的用户认证功能，包括：
 * - 用户登录
 * - 用户注册
 * - 用户状态管理
 * - 权限控制
 */

// 存储键名
const AUTH_KEYS = {
    USERS: 'football_users',
    CURRENT_USER: 'football_current_user',
    REMEMBER_ME: 'football_remember_me'
};

// 用户角色权限
const USER_ROLES = {
    VIEWER: 'viewer',  // 只能查看数据
    EDITOR: 'editor',  // 可以编辑部分数据
    ADMIN: 'admin'     // 可以管理所有数据和用户
};

// 认证管理类
class AuthManager {
    constructor() {
        // 初始化状态
        this.currentUser = null;

        // 加载当前用户信息
        this.loadCurrentUser();

        // 如果没有任何用户，创建默认管理员账号
        this.initDefaultAdmin();
    }

    /**
     * 初始化默认管理员账号
     */
    initDefaultAdmin () {
        const users = this.getAllUsers();

        if (users.length === 0) {
            // 创建默认管理员账号
            const adminUser = {
                id: this.generateUserId(),
                username: 'admin',
                password: this.hashPassword('admin123'),
                role: USER_ROLES.ADMIN,
                createdAt: new Date().toISOString()
            };

            users.push(adminUser);
            this.saveUsers(users);
            console.log('已创建默认管理员账号: admin / admin123');
        }
    }

    /**
     * 加载当前用户信息
     */
    loadCurrentUser () {
        // 检查本地存储中是否有当前用户信息
        const currentUserJson = localStorage.getItem(AUTH_KEYS.CURRENT_USER);

        if (currentUserJson) {
            try {
                const userData = JSON.parse(currentUserJson);
                // 不存储密码在当前用户对象中
                delete userData.password;
                this.currentUser = userData;

                // 更新用户登录状态
                this.updateLoginState(true);
            } catch (e) {
                console.error('加载用户信息失败:', e);
                localStorage.removeItem(AUTH_KEYS.CURRENT_USER);
            }
        } else if (localStorage.getItem(AUTH_KEYS.REMEMBER_ME)) {
            // 检查是否有记住我的信息
            try {
                const rememberData = JSON.parse(localStorage.getItem(AUTH_KEYS.REMEMBER_ME));
                this.login(rememberData.username, rememberData.token, true);
            } catch (e) {
                console.error('自动登录失败:', e);
                localStorage.removeItem(AUTH_KEYS.REMEMBER_ME);
            }
        }
    }

    /**
     * 获取所有用户
     */
    getAllUsers () {
        const usersJson = localStorage.getItem(AUTH_KEYS.USERS);
        return usersJson ? JSON.parse(usersJson) : [];
    }

    /**
     * 保存用户数据
     */
    saveUsers (users) {
        localStorage.setItem(AUTH_KEYS.USERS, JSON.stringify(users));
    }

    /**
     * 生成用户ID
     */
    generateUserId () {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
    }

    /**
     * 简单的密码哈希（实际应用中应使用更安全的方法）
     */
    hashPassword (password) {
        // 简单的密码哈希实现，实际应用中应使用更安全的方法
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString(16);
    }

    /**
     * 用户登录
     */
    login (username, password, rememberMe = false) {
        const users = this.getAllUsers();
        const user = users.find(u => u.username === username);

        if (!user) {
            return { success: false, message: '用户名不存在' };
        }

        const hashedPassword = this.hashPassword(password);
        if (user.password !== hashedPassword) {
            return { success: false, message: '密码错误' };
        }

        // 登录成功
        const userInfo = { ...user };
        delete userInfo.password; // 不存储密码在当前用户对象中

        this.currentUser = userInfo;
        localStorage.setItem(AUTH_KEYS.CURRENT_USER, JSON.stringify(userInfo));

        // 记住我功能
        if (rememberMe) {
            const rememberToken = this.generateRememberToken(username, hashedPassword);
            localStorage.setItem(AUTH_KEYS.REMEMBER_ME, JSON.stringify({
                username,
                token: rememberToken
            }));
        } else {
            localStorage.removeItem(AUTH_KEYS.REMEMBER_ME);
        }

        // 更新登录状态
        this.updateLoginState(true);

        return { success: true, user: userInfo };
    }

    /**
     * 生成"记住我"令牌
     */
    generateRememberToken (username, passwordHash) {
        // 简单的令牌生成，实际应用中应使用更安全的方法
        return btoa(`${username}:${passwordHash}:${Date.now()}`);
    }

    /**
     * 用户注册
     */
    register (username, password, role = USER_ROLES.VIEWER) {
        // 验证用户名和密码
        if (!username || username.length < 3) {
            return { success: false, message: '用户名至少需要3个字符' };
        }

        if (!password || password.length < 6) {
            return { success: false, message: '密码至少需要6个字符' };
        }

        const users = this.getAllUsers();

        // 检查用户名是否已存在
        if (users.some(u => u.username === username)) {
            return { success: false, message: '用户名已存在' };
        }

        // 创建新用户
        const newUser = {
            id: this.generateUserId(),
            username,
            password: this.hashPassword(password),
            role,
            createdAt: new Date().toISOString()
        };

        users.push(newUser);
        this.saveUsers(users);

        // 注册成功后自动登录
        return this.login(username, password);
    }

    /**
     * 用户登出
     */
    logout () {
        this.currentUser = null;
        localStorage.removeItem(AUTH_KEYS.CURRENT_USER);
        localStorage.removeItem(AUTH_KEYS.REMEMBER_ME);

        // 更新登录状态
        this.updateLoginState(false);

        // 重定向到登录页
        window.location.href = 'login.html';
    }

    /**
     * 检查用户是否已登录
     */
    isLoggedIn () {
        return !!this.currentUser;
    }

    /**
     * 获取当前用户信息
     */
    getCurrentUser () {
        return this.currentUser;
    }

    /**
     * 检查当前用户是否具有特定角色
     */
    hasRole (role) {
        if (!this.isLoggedIn()) return false;

        if (role === USER_ROLES.VIEWER) {
            // 所有登录用户都有观看权限
            return true;
        }

        if (role === USER_ROLES.EDITOR) {
            // 编辑者和管理员有编辑权限
            return this.currentUser.role === USER_ROLES.EDITOR ||
                this.currentUser.role === USER_ROLES.ADMIN;
        }

        if (role === USER_ROLES.ADMIN) {
            // 只有管理员有管理权限
            return this.currentUser.role === USER_ROLES.ADMIN;
        }

        return false;
    }

    /**
     * 更新登录状态
     */
    updateLoginState (isLoggedIn) {
        // 触发登录状态变更事件
        const event = new CustomEvent('authStateChanged', {
            detail: { isLoggedIn, user: this.currentUser }
        });
        document.dispatchEvent(event);
    }
}

// 创建认证管理器实例
const authManager = new AuthManager();

// 在登录页面初始化事件处理
document.addEventListener('DOMContentLoaded', function () {
    // 检查是否在登录页面
    if (window.location.pathname.includes('login.html')) {
        initLoginPage();
    } else {
        // 其他页面检查登录状态
        checkLoginStatus();
    }
});

/**
 * 初始化登录页面
 */
function initLoginPage () {
    // 如果已经登录，重定向到首页
    if (authManager.isLoggedIn()) {
        window.location.href = 'index.html';
        return;
    }

    // 登录表单
    const loginForm = document.querySelector('.login-form');
    const loginBtn = document.getElementById('login-btn');
    const registerBtn = document.getElementById('register-btn');
    const loginError = document.getElementById('login-error');

    // 注册表单
    const registerForm = document.querySelector('.register-form');
    const createAccountBtn = document.getElementById('create-account-btn');
    const backToLoginBtn = document.getElementById('back-to-login-btn');
    const registerError = document.getElementById('register-error');

    // 切换到注册表单
    registerBtn.addEventListener('click', function () {
        loginForm.style.display = 'none';
        registerForm.style.display = 'block';
        loginError.textContent = '';
    });

    // 切换回登录表单
    backToLoginBtn.addEventListener('click', function () {
        registerForm.style.display = 'none';
        loginForm.style.display = 'block';
        registerError.textContent = '';
    });

    // 登录按钮点击
    loginBtn.addEventListener('click', function () {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        if (!username || !password) {
            loginError.textContent = '请输入用户名和密码';
            return;
        }

        const result = authManager.login(username, password, rememberMe);

        if (result.success) {
            window.location.href = 'index.html';
        } else {
            loginError.textContent = result.message;
        }
    });

    // 回车键登录
    document.getElementById('password').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            loginBtn.click();
        }
    });

    // 创建账号按钮点击
    createAccountBtn.addEventListener('click', function () {
        const username = document.getElementById('new-username').value.trim();
        const password = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        const role = document.getElementById('user-role').value;

        if (!username || !password || !confirmPassword) {
            registerError.textContent = '请填写所有必填字段';
            return;
        }

        if (password !== confirmPassword) {
            registerError.textContent = '两次输入的密码不一致';
            return;
        }

        const result = authManager.register(username, password, role);

        if (result.success) {
            window.location.href = 'index.html';
        } else {
            registerError.textContent = result.message;
        }
    });
}

/**
 * 检查登录状态并处理未登录情况
 */
function checkLoginStatus () {
    // 如果未登录，重定向到登录页面
    if (!authManager.isLoggedIn()) {
        window.location.href = 'login.html';
        return;
    }

    // 已登录，更新页面上的用户信息
    updateUserInfo();

    // 根据用户权限控制页面元素
    updatePageByPermission();
}

/**
 * 更新页面上的用户信息显示
 */
function updateUserInfo () {
    const user = authManager.getCurrentUser();

    // 检查页面上是否有用户信息区域
    const headerActions = document.querySelector('.header-actions');
    if (!headerActions) return;

    // 创建用户信息显示
    const userInfoHtml = `
        <div class="user-info">
            <span class="user-name">${user.username}</span>
            <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出</a>
        </div>
    `;

    // 添加用户信息到页面
    const existingUserInfo = headerActions.querySelector('.user-info');
    if (existingUserInfo) {
        existingUserInfo.outerHTML = userInfoHtml;
    } else {
        headerActions.innerHTML += userInfoHtml;
    }

    // 绑定登出按钮事件
    document.querySelector('.logout-btn').addEventListener('click', function (e) {
        e.preventDefault();
        authManager.logout();
    });
}

/**
 * 根据用户权限更新页面元素
 */
function updatePageByPermission () {
    const user = authManager.getCurrentUser();

    // 隐藏没有权限的元素
    if (!authManager.hasRole(USER_ROLES.ADMIN)) {
        // 非管理员用户隐藏管理相关功能
        const adminElements = document.querySelectorAll('.admin-only');
        adminElements.forEach(el => el.style.display = 'none');
    }

    if (!authManager.hasRole(USER_ROLES.EDITOR) && !authManager.hasRole(USER_ROLES.ADMIN)) {
        // 非编辑者和管理员隐藏编辑相关功能
        const editorElements = document.querySelectorAll('.editor-only');
        editorElements.forEach(el => el.style.display = 'none');
    }
}

// 导出认证管理器以供其他模块使用
window.authManager = authManager; 