<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比赛 - 中国青少年足球数据平台</title>
    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css">
    <!-- 页面特定样式 -->
    <link rel="stylesheet" href="css/pages/matches.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- 导航栏由Navigation组件填充 -->
    <header></header>

    <main>
        <div class="page-header">
            <h2><i class="fas fa-futbol"></i> 比赛</h2>
        </div>

        <!-- 搜索栏 -->
        <div id="search-container"></div>

        <!-- 筛选器 -->
        <div class="filter-section">
            <select id="match-status-filter" class="filter-select">
                <option value="all">所有状态</option>
                <option value="upcoming">即将开始</option>
                <option value="live">正在进行</option>
                <option value="completed">已结束</option>
            </select>
            <select id="match-tournament-filter" class="filter-select">
                <option value="all">所有赛事</option>
                <!-- 赛事选项将由JavaScript动态加载 -->
            </select>
        </div>

        <!-- 比赛列表 -->
        <div id="matches-container" class="matches-grid">
            <!-- 比赛卡片将由JavaScript动态加载 -->
        </div>

        <!-- 比赛详情 -->
        <div id="match-detail" class="match-detail" style="display: none;">
            <!-- 比赛详细信息将由JavaScript动态加载 -->
        </div>
    </main>

    <!-- 底部导航由Navigation组件填充 -->
    <footer></footer>

    <!-- 组件 -->
    <script src="components/Navigation.js"></script>
    <script src="components/SearchBar.js"></script>
    <script src="components/Card.js"></script>
    <!-- 数据和应用逻辑 -->
    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script src="js/init-data.js"></script>
    <script src="js/app.js"></script>
    <script>
        // 初始化组件
        document.addEventListener('DOMContentLoaded', function () {
            // 初始化导航
            Navigation.init();

            // 初始化搜索栏
            document.getElementById('search-container').innerHTML = SearchBar.render('match-search', '搜索比赛...');
            SearchBar.init('match-search', searchMatches);

            // 初始化筛选器
            initMatchFilters();

            // 检查是否有比赛ID参数
            const urlParams = new URLSearchParams(window.location.search);
            const matchId = urlParams.get('id');

            if (matchId) {
                // 显示比赛详情
                showMatchDetail(matchId);
            } else {
                // 加载比赛列表
                loadMatches();
            }
        });
    </script>
</body>

</html>